const BOOKING_STATE_KEY = "bookingState";
const LAST_BOOKING_STATE_KEY = "lastBookingState";
const BACKUP_STATE_KEY = "bookingStateBackup";
const STATE_VALIDITY_DURATION = 30 * 60 * 1000; // 30 minutes
const AUTO_SAVE_INTERVAL = 30 * 1000; // 30 seconds

export const BookingStateManager = {
  saveBookingState: (state) => {
    try {
      const stateWithTimestamp = {
        ...state,
        timestamp: Date.now(),
        lastModified: Date.now(),
      };

      // Save to sessionStorage
      sessionStorage.setItem(
        BOOKING_STATE_KEY,
        JSON.stringify(stateWithTimestamp)
      );

      // Backup to localStorage
      localStorage.setItem(BACKUP_STATE_KEY, JSON.stringify(stateWithTimestamp));

      return true;
    } catch (error) {
      console.error("Error saving booking state:", error);
      return false;
    }
  },

  getBookingState: () => {
    try {
      // Try sessionStorage first
      const savedState = sessionStorage.getItem(BOOKING_STATE_KEY);
      let state = null;

      if (savedState) {
        state = JSON.parse(savedState);
      } else {
        // Try recovering from localStorage backup
        const backupState = localStorage.getItem(BACKUP_STATE_KEY);
        if (backupState) {
          state = JSON.parse(backupState);
          // Restore to session storage if backup is still valid
          if (Date.now() - state.timestamp <= STATE_VALIDITY_DURATION) {
            sessionStorage.setItem(BOOKING_STATE_KEY, backupState);
          }
        }
      }

      if (state) {
        // Check if state is still valid
        if (Date.now() - state.timestamp > STATE_VALIDITY_DURATION) {
          BookingStateManager.clearBookingState();
          return null;
        }
        return state;
      }
      return null;
    } catch (error) {
      console.error("Error retrieving booking state:", error);
      return null;
    }
  },

  autoSaveBookingState: (state) => {
    if (!state) return null;

    const currentState = BookingStateManager.getBookingState();
    // Only save if state has changed
    if (
      !currentState ||
      JSON.stringify(currentState) !== JSON.stringify(state) ||
      Date.now() - (currentState.lastModified || 0) >= AUTO_SAVE_INTERVAL
    ) {
      return BookingStateManager.saveBookingState(state);
    }
    return false;
  },

  saveLastBookingState: (state) => {
    try {
      const stateWithTimestamp = {
        ...state,
        timestamp: Date.now(),
      };
      sessionStorage.setItem(
        LAST_BOOKING_STATE_KEY,
        JSON.stringify(stateWithTimestamp)
      );
      localStorage.setItem(
        LAST_BOOKING_STATE_KEY,
        JSON.stringify(stateWithTimestamp)
      );
    } catch (error) {
      console.error("Error saving last booking state:", error);
    }
  },

  getLastBookingState: () => {
    try {
      // Try sessionStorage first
      const savedState =
        sessionStorage.getItem(LAST_BOOKING_STATE_KEY) ||
        localStorage.getItem(LAST_BOOKING_STATE_KEY);
      return savedState ? JSON.parse(savedState) : null;
    } catch (error) {
      console.error("Error retrieving last booking state:", error);
      return null;
    }
  },

  clearBookingState: () => {
    try {
      sessionStorage.removeItem(BOOKING_STATE_KEY);
      localStorage.removeItem(BACKUP_STATE_KEY);
    } catch (error) {
      console.error("Error clearing booking state:", error);
    }
  },

  clearAllBookingStates: () => {
    try {
      sessionStorage.removeItem(BOOKING_STATE_KEY);
      sessionStorage.removeItem(LAST_BOOKING_STATE_KEY);
      localStorage.removeItem(BACKUP_STATE_KEY);
      localStorage.removeItem(LAST_BOOKING_STATE_KEY);
    } catch (error) {
      console.error("Error clearing all booking states:", error);
    }
  },
};
