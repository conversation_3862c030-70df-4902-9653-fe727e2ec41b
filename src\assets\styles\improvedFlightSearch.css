/* Improved Flight Search Styles */

.flight-search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-card {
  background: rgba(255, 255, 255, 0.98);
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.search-card-header {
  background-color: #007bff;
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.search-card-header h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: bold;
}

.search-card-body {
  padding: 2rem;
}

.form-label {
  color: #37474f;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.form-control,
.form-select {
  height: 48px;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* Trip Type Selector */
.trip-type-selector {
  margin-bottom: 2rem;
}

.trip-type-selector .btn-group {
  background: #f5f5f5;
  padding: 0.5rem;
  border-radius: 12px;
}

.trip-type-selector .btn {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  flex: 1;
}

.trip-type-selector .btn-check:checked + .btn {
  background: #1976d2;
  color: white;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
  transform: translateY(-1px);
}

/* Location Inputs */
.location-input-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.location-swap-button {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 2px solid #e0e0e0;
  color: #1976d2;
  transition: all 0.3s ease;
  margin-top: 2rem;
}

.location-swap-button:hover {
  background: #1976d2;
  color: white;
  transform: rotate(180deg);
}

/* Date Inputs */
.date-inputs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.date-field {
  flex: 1;
  position: relative;
}

.date-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--dark);
}

/* Passenger Inputs */
.passenger-inputs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 0; /* Keep passenger inputs below the calendar */
}

.passenger-field {
  flex: 1;
  position: relative;
  margin-bottom: 1rem;
}

.passenger-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--dark);
}

.passenger-counter {
  display: flex;
  align-items: center;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.passenger-counter button {
  width: 48px;
  height: 48px;
  border: none;
  background: #f5f5f5;
  color: #37474f;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.passenger-counter button:hover:not(:disabled) {
  background: #1976d2;
  color: white;
}

.passenger-counter button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.passenger-counter-value {
  width: 60px;
  text-align: center;
  font-weight: 500;
  border: none;
  background: transparent;
}

/* Input group styling */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  width: 100%;
  background: white;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  overflow: hidden;
}

.input-group:hover {
  border-color: #d1d5db;
}

.input-group:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Input group buttons */
.input-group .btn {
  padding: 0.5rem 1rem;
  border: none;
  background: #f3f4f6;
  color: #374151;
  transition: all 0.2s ease;
  z-index: 0;
}

.input-group .btn:hover {
  background: #e5e7eb;
}

.input-group .btn:active {
  background: #d1d5db;
}

/* Center input */
.input-group input {
  text-align: center;
  border: none;
  background: transparent;
  width: 60px;
  padding: 0.5rem;
}

.input-group input:focus {
  outline: none;
  box-shadow: none;
}

/* Search Button */
.search-button {
  height: 56px;
  background: linear-gradient(90deg, #1976d2 0%, #1565c0 100%);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.search-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.search-button:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
}

/* Flexible Dates */
.flexible-dates {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--secondary-light);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.flexible-dates-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.flexible-dates-section:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.1);
}

.flexible-dates-toggle {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.flexible-dates-toggle .form-check-input {
  width: 3rem;
  height: 1.5rem;
  margin-right: 0.75rem;
}

.flexible-dates-toggle .form-check-input:checked {
  background-color: #1976d2;
  border-color: #1976d2;
}

.flexible-dates-options {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.flexible-dates-matrix {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.5rem;
  margin-top: 1rem;
}

.date-cell {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-cell:hover {
  border-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.1);
}

.date-cell.selected {
  background: #e3f2fd;
  border-color: #1976d2;
}

.date-cell.has-flights {
  border-color: #4caf50;
}

.date-cell.no-flights {
  background: #f5f5f5;
  cursor: not-allowed;
}

.date-cell .date {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.date-cell .price {
  font-size: 0.85rem;
  color: #4caf50;
  font-weight: 600;
}

.date-cell .no-price {
  font-size: 0.85rem;
  color: #9e9e9e;
}

/* Flight Results */
.flight-results-container {
  margin-top: 2rem;
}

/* Flight Results Card */
.flight-item {
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.flight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.flight-item.selected-flight {
  border: 2px solid #2196f3;
  background-color: #e3f2fd;
}

/* Airline Info */
.airline-logo-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
}

.airline-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: #37474f;
  margin-top: 0.5rem;
  text-align: center;
}

/* Flight Details */
.flight-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.departure-details,
.arrival-details {
  flex: 1;
}

.flight-time {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1976d2;
}

.airport-code {
  font-weight: 500;
  color: #37474f;
}

.airport-city {
  font-size: 0.9rem;
  color: #6b7280;
}

/* Flight Duration and Stops */
.flight-duration-container {
  text-align: center;
  position: relative;
  padding: 0 1rem;
}

.duration-time {
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.flight-line {
  height: 2px;
  background: #e5e7eb;
  position: relative;
  margin: 0.5rem 0;
}

.flight-line::after {
  content: "✈️";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.25rem;
  background: #fff;
  padding: 0 0.5rem;
}

.stops-label {
  font-size: 0.85rem;
  color: #6b7280;
}

/* Price and Action */
.price-container {
  text-align: right;
}

.flight-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.select-flight-btn {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.select-flight-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

/* Error and Validation States */
.validation-error {
  background: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #dc2626;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

.validation-error .fas {
  color: #dc2626;
}

.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #dc2626;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc2626'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc2626' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1.5rem;
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.invalid-feedback {
  display: block;
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.375rem;
}

/* Success States */
.form-control.is-valid,
.form-select.is-valid {
  border-color: #059669;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23059669' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1.5rem;
}

.form-control.is-valid:focus,
.form-select.is-valid:focus {
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.valid-feedback {
  display: block;
  color: #059669;
  font-size: 0.875rem;
  margin-top: 0.375rem;
}

/* Tooltips */
.form-tooltip {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  background: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  z-index: 100;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.form-tooltip::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 1rem;
  border: 6px solid transparent;
  border-bottom-color: #1f2937;
}

.form-control:hover + .form-tooltip,
.form-select:hover + .form-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Shake Animation for Errors */
@keyframes shake {
  10%,
  90% {
    transform: translateX(-1px);
  }
  20%,
  80% {
    transform: translateX(2px);
  }
  30%,
  50%,
  70% {
    transform: translateX(-4px);
  }
  40%,
  60% {
    transform: translateX(4px);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .search-card {
    background: rgba(30, 30, 30, 0.98);
  }

  .form-label {
    color: #e0e0e0;
  }

  .form-control,
  .form-select {
    background: #2c2c2c;
    border-color: #424242;
    color: #ffffff;
  }

  .trip-type-selector .btn-group {
    background: #2c2c2c;
  }

  .passenger-counter {
    border-color: #424242;
  }

  .passenger-counter button {
    background: #2c2c2c;
    color: #e0e0e0;
  }

  .passenger-type-card,
  .cabin-class-card {
    background: #2c2c2c;
    border-color: #424242;
  }

  .passenger-type-card:hover,
  .cabin-class-card:hover {
    border-color: #1976d2;
    background: #333333;
  }

  .passenger-type-card .fas {
    color: #64b5f6;
  }

  .cabin-class-card .form-select {
    background-color: #2c2c2c;
    border-color: #424242;
    color: #ffffff;
  }

  .cabin-class-card .form-select:focus {
    border-color: #1976d2;
  }

  .flight-item {
    background: #2c2c2c;
    border-color: #424242;
  }

  .flight-item:hover {
    border-color: #1976d2;
    background: #333333;
  }

  .flight-item.selected-flight {
    background-color: #0d47a1;
    border-color: #42a5f5;
  }

  .airline-logo-container {
    background: #333333;
  }

  .airline-name,
  .airport-code {
    color: #e0e0e0;
  }

  .airport-city,
  .stops-label {
    color: #9e9e9e;
  }

  .flight-time {
    color: #64b5f6;
  }

  .flight-line {
    background: #424242;
  }

  .flight-line::after {
    background: #2c2c2c;
  }

  .flight-price {
    color: #64b5f6;
  }

  .flexible-dates-section {
    background: #1a1a1a;
    border-color: #424242;
  }

  .date-cell {
    background: #2c2c2c;
    border-color: #424242;
    color: #e0e0e0;
  }

  .date-cell:hover {
    border-color: #1976d2;
    background: #333333;
  }

  .date-cell.selected {
    background: #0d47a1;
    border-color: #42a5f5;
  }

  .date-cell.has-flights {
    border-color: #66bb6a;
  }

  .date-cell.no-flights {
    background: #1a1a1a;
  }

  .date-cell .price {
    color: #66bb6a;
  }

  .date-cell .no-price {
    color: #757575;
  }

  .validation-error {
    background: rgba(220, 38, 38, 0.1);
    border-color: rgba(220, 38, 38, 0.2);
    color: #ef4444;
  }

  .form-control.is-invalid,
  .form-select.is-invalid {
    border-color: #ef4444;
    background-color: rgba(220, 38, 38, 0.1);
  }

  .invalid-feedback {
    color: #ef4444;
  }

  .form-control.is-valid,
  .form-select.is-valid {
    border-color: #10b981;
    background-color: rgba(5, 150, 105, 0.1);
  }

  .valid-feedback {
    color: #10b981;
  }

  .form-tooltip {
    background: #374151;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .form-tooltip::before {
    border-bottom-color: #374151;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Loading States */
.search-loading {
  position: relative;
}

.search-loading::after {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 11;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Passenger Section Styles */
.passenger-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
}

.passenger-type-card {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.25rem;
  height: 100%;
  transition: all 0.2s ease;
}

.passenger-type-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.passenger-type-header .form-label {
  margin-bottom: 0;
  font-weight: 500;
  color: #334155;
  flex: 1;
}

.passenger-type-header small {
  color: #64748b;
  font-size: 0.825rem;
}

.passenger-type-header .fas {
  color: #3b82f6;
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

.passenger-type-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.passenger-type-controls .input-group {
  width: 120px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.passenger-type-controls .input-group .btn {
  border: none;
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.passenger-type-controls .input-group input {
  border: none;
  text-align: center;
  font-weight: 500;
  color: #334155;
  width: 40px;
  padding: 0.5rem 0;
  background: #ffffff;
}

/* Error state */
.passenger-type-card .invalid-feedback {
  font-size: 0.825rem;
  color: #ef4444;
  margin-top: 0.5rem;
}

/* Hover state */
.passenger-type-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

/* Passenger Type Grid Layout */
.passenger-type-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.passenger-type-card {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.passenger-type-header {
  margin-bottom: 0.75rem;
}

.passenger-type-header .form-label {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  font-weight: 500;
  color: #334155;
  gap: 0.5rem;
}

.passenger-type-header i {
  color: #3b82f6;
  font-size: 1rem;
}

.passenger-type-header small {
  color: #64748b;
  font-size: 0.75rem;
  margin-left: 0.25rem;
}

.passenger-type-controls .input-group {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.passenger-type-controls .btn {
  border: none;
  background: #f1f5f9;
  color: #475569;
  padding: 0.375rem;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.passenger-type-controls .btn:hover:not(:disabled) {
  background: #e2e8f0;
}

.passenger-type-controls .form-control {
  border: none;
  text-align: center;
  font-weight: 500;
  color: #334155;
  width: 40px;
  padding: 0.375rem 0;
  background: #ffffff;
}

.passenger-type-controls .form-select {
  border-color: #e2e8f0;
  color: #334155;
  font-weight: 500;
}

/* Responsive layout */
@media (max-width: 992px) {
  .passenger-type-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .passenger-type-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .passenger-type-card {
    background: #1e293b;
    border-color: #334155;
  }

  .passenger-type-header .form-label {
    color: #e2e8f0;
  }

  .passenger-type-header i {
    color: #60a5fa;
  }

  .passenger-type-header small {
    color: #94a3b8;
  }

  .passenger-type-controls .input-group {
    border-color: #334155;
  }

  .passenger-type-controls .btn {
    background: #1e293b;
    color: #e2e8f0;
  }

  .passenger-type-controls .btn:hover:not(:disabled) {
    background: #334155;
  }

  .passenger-type-controls .form-control {
    background: #1e293b;
    color: #e2e8f0;
  }

  .passenger-type-controls .form-select {
    background-color: #1e293b;
    border-color: #334155;
    color: #e2e8f0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .passenger-section {
    padding: 1rem;
  }

  .passenger-type-card {
    padding: 1rem;
  }

  .row-cols-md-4 > * {
    margin-bottom: 1rem;
  }
}

.search-row {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  padding: 1rem;
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 1.5rem;
}

.search-field {
  flex: 1;
  min-width: 200px;
}

.search-field.date-field {
  flex: 1.2;
}

.search-field .form-label {
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
}

.swap-button-wrapper {
  padding-bottom: 0.5rem;
}

.location-swap-button {
  margin: 0;
  transform: translateY(-4px);
}

/* Improved Search Fields Layout */
.search-fields-container {
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-fields-container .row {
  margin: 0 -0.75rem;
}

.search-fields-container [class*="col-"] {
  padding: 0 0.75rem;
  display: flex;
  flex-direction: column;
}

.search-fields-container .form-label {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.5rem;
  white-space: nowrap;
}

.search-fields-container .form-control {
  height: 48px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  font-size: 0.95rem;
}

.swap-button {
  width: 40px;
  height: 40px;
  margin-top: 32px; /* Aligns with input fields, accounting for label height */
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.swap-button:hover {
  transform: rotate(180deg);
  background-color: #1976d2;
  color: white;
  border-color: #1976d2;
}

/* Date picker specific alignment */
.search-fields-container .date-range-picker-container,
.search-fields-container .datepicker-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-fields-container .date-range-picker-container .row {
  flex: 1;
  margin: 0 -0.375rem;
}

.search-fields-container .date-range-picker-container [class*="col-"] {
  padding: 0 0.375rem;
}

/* Passenger Type Row */
.passenger-type-row {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: #fff;
}

.passenger-type-label {
  display: flex;
  align-items: center;
}

.passenger-type-label .form-label {
  font-weight: 500;
  margin-right: 1rem;
  white-space: nowrap;
}

.passenger-type-controls {
  width: 180px;
}

.passenger-type-controls .input-group {
  width: 100%;
}

.passenger-type-controls .form-control {
  text-align: center;
  background-color: #fff;
}

.passenger-type-controls .form-select {
  min-width: 180px;
}

/* Make the controls more compact */
.passenger-type-controls .btn {
  padding: 0.25rem 0.5rem;
}

.passenger-type-controls .form-control {
  padding: 0.25rem 0.5rem;
  height: auto;
}

/* Ensure proper spacing for icons and labels */
.passenger-type-label i {
  margin-right: 0.5rem;
  width: 1.25rem;
  text-align: center;
}

.passenger-type-label small {
  color: #6c757d;
  font-size: 0.875rem;
}
