/* Fixed Navbar styling - Updated to match homepage colors */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
  box-shadow: 0 2px 4px rgba(2, 48, 71, 0.1);
  padding: 1rem 0;
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--primary-medium)
  );
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  width: 100%;
}

/* Navbar scrolled state */
.navbar.scrolled {
  padding: 0.5rem 0;
  background: linear-gradient(
    135deg,
    rgba(2, 48, 71, 0.95),
    rgba(33, 158, 188, 0.95)
  );
  box-shadow: 0 4px 12px rgba(2, 48, 71, 0.2);
}

/* Ensure navbar stays above other content */
.navbar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: -1;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--white) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-link {
  font-weight: 500;
  margin: 0 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--white) !important;
}

.nav-link:hover {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 183, 3, 0.3);
}

/* Active nav link styling */
.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff !important;
}

/* Dropdown menu styling for fixed navbar */
.dropdown-menu {
  margin-top: 0.5rem;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Mobile responsive adjustments */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background-color: rgba(33, 158, 188, 0.98);
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .nav-link {
    margin: 0.25rem 0;
    padding: 0.75rem 1rem;
  }
}

/* Ensure proper spacing for mobile navbar toggle */
@media (max-width: 767.98px) {
  .navbar-brand {
    font-size: 1.25rem;
  }

  .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
  }

  .navbar-toggler:focus {
    box-shadow: none;
  }
}

/* Brand logo styling */
.navbar-brand {
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  color: #ffffff !important;
  transform: scale(1.05);
}

/* Smooth transitions for all navbar elements */
.navbar-nav .nav-link,
.navbar-brand,
.dropdown-toggle {
  transition: all 0.3s ease;
}

/* Loading state for navbar */
.navbar.loading {
  opacity: 0.8;
}

/* Ensure navbar is always visible */
.navbar {
  visibility: visible;
  opacity: 1;
}
