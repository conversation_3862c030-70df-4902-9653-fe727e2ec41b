import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAppContext, appActions } from "../../context/AppContext";
import "../../assets/styles/Navbar.css";

const Navbar = () => {
  const { state, dispatch } = useAppContext();
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Handle currency change
  const handleCurrencyChange = (currency) => {
    dispatch(appActions.setCurrency(currency));
    setShowCurrencyDropdown(false);
  };

  // Handle scroll detection for navbar styling
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Helper function to check if nav link is active
  const isActiveLink = (path) => {
    return location.pathname === path;
  };

  return (
    <>
      <nav
        className={`navbar navbar-expand-lg navbar-dark ${isScrolled ? "scrolled" : ""}`}
      >
        <div className="container">
          <Link className="navbar-brand" to="/">
            Tripstar
          </Link>

          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav me-auto">
              <li className="nav-item">
                <Link
                  className={`nav-link ${isActiveLink("/") ? "active" : ""}`}
                  to="/"
                >
                  Home
                </Link>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link ${isActiveLink("/flights") ? "active" : ""}`}
                  to="/flights"
                >
                  Flights
                </Link>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link ${isActiveLink("/hotels") ? "active" : ""}`}
                  to="/hotels"
                >
                  Hotels
                </Link>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link ${isActiveLink("/contact") ? "active" : ""}`}
                  to="/contact"
                >
                  Contact
                </Link>
              </li>
            </ul>

            <ul className="navbar-nav">
              {/* Recent Searches Dropdown */}
              {state.recentSearches.length > 0 && (
                <li className="nav-item dropdown">
                  <a
                    className="nav-link dropdown-toggle"
                    href="#"
                    role="button"
                    data-bs-toggle="dropdown"
                  >
                    <i className="fas fa-history me-1"></i> Recent
                  </a>
                  <ul className="dropdown-menu dropdown-menu-end">
                    {state.recentSearches.map((search, index) => (
                      <li key={index}>
                        <Link
                          className="dropdown-item"
                          to="/flights"
                          state={search}
                        >
                          {search.origin} → {search.destination}
                        </Link>
                      </li>
                    ))}
                    <li>
                      <hr className="dropdown-divider" />
                    </li>
                    <li>
                      <button
                        className="dropdown-item text-danger"
                        onClick={() =>
                          dispatch(appActions.clearRecentSearches())
                        }
                      >
                        Clear All
                      </button>
                    </li>
                  </ul>
                </li>
              )}

              {/* Currency Selector */}
              {/* Removed the currency selector dropdown */}

              {/* Retrieve Booking */}
              <li className="nav-item">
                <Link className="nav-link" to="/retrieve-booking">
                  <i className="fas fa-search me-1"></i> Retrieve Booking
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
