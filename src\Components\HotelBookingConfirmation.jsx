import React, { useState } from 'react';
import { Card, Row, Col, Button, Badge, Alert, Table } from 'react-bootstrap';
import { FaCheckCircle, FaHotel, FaCalendarAlt, FaUsers, FaBed, FaEnvelope, FaPhone, FaPrint, FaDownload } from 'react-icons/fa';

const HotelBookingConfirmation = ({ 
  bookingData, 
  hotelOffer, 
  selectedRooms, 
  guests, 
  searchParams,
  onNewSearch,
  onViewBookings 
}) => {
  const [showFullDetails, setShowFullDetails] = useState(false);

  const hotel = hotelOffer?.hotel;
  const confirmationNumber = bookingData?.confirmationNumber || bookingData?.data?.providerConfirmationId;

  // Calculate total price
  const calculateTotalPrice = () => {
    if (!selectedRooms) return 0;
    return selectedRooms.reduce((total, room) => {
      return total + (parseFloat(room.price?.total || 0) * (room.quantity || 1));
    }, 0);
  };

  // Calculate nights
  const calculateNights = () => {
    if (searchParams?.checkInDate && searchParams?.checkOutDate) {
      const checkIn = new Date(searchParams.checkInDate);
      const checkOut = new Date(searchParams.checkOutDate);
      const diffTime = checkOut - checkIn;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }
    return 1;
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Handle print booking
  const handlePrint = () => {
    window.print();
  };

  // Handle download booking (simplified - would typically generate PDF)
  const handleDownload = () => {
    const bookingDetails = {
      confirmationNumber,
      hotel: hotel?.name,
      checkIn: searchParams?.checkInDate,
      checkOut: searchParams?.checkOutDate,
      guests: guests?.length || 0,
      totalPrice: calculateTotalPrice()
    };
    
    const dataStr = JSON.stringify(bookingDetails, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hotel-booking-${confirmationNumber}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const totalPrice = calculateTotalPrice();
  const nights = calculateNights();

  return (
    <div className="hotel-booking-confirmation">
      {/* Success Header */}
      <Card className="mb-4 border-success">
        <Card.Body className="text-center py-5">
          <FaCheckCircle size={60} className="text-success mb-3" />
          <h2 className="text-success mb-2">Booking Confirmed!</h2>
          <p className="lead mb-3">
            Your hotel reservation has been successfully confirmed.
          </p>
          <div className="confirmation-number">
            <h4 className="mb-0">
              Confirmation Number: 
              <Badge bg="success" className="ms-2 fs-5">
                {confirmationNumber}
              </Badge>
            </h4>
            <small className="text-muted">
              Please save this confirmation number for your records
            </small>
          </div>
        </Card.Body>
      </Card>

      {/* Booking Details */}
      <Row>
        <Col lg={8}>
          {/* Hotel Information */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <FaHotel className="me-2" />
                Hotel Information
              </h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={8}>
                  <h6 className="mb-2">{hotel?.name}</h6>
                  <p className="text-muted mb-2">
                    {hotel?.address?.lines?.join(', ')}
                  </p>
                  <p className="text-muted mb-2">
                    {hotel?.address?.cityName}, {hotel?.address?.postalCode}
                  </p>
                  <p className="text-muted mb-0">
                    {hotel?.address?.countryCode}
                  </p>
                  
                  {hotel?.contact && (
                    <div className="mt-3">
                      {hotel.contact.phone && (
                        <p className="mb-1">
                          <FaPhone className="me-2" />
                          {hotel.contact.phone}
                        </p>
                      )}
                      {hotel.contact.email && (
                        <p className="mb-0">
                          <FaEnvelope className="me-2" />
                          {hotel.contact.email}
                        </p>
                      )}
                    </div>
                  )}
                </Col>
                <Col md={4} className="text-end">
                  {hotel?.rating && (
                    <div className="hotel-rating mb-2">
                      <Badge bg="warning" text="dark">
                        {hotel.rating} Star Hotel
                      </Badge>
                    </div>
                  )}
                  {hotel?.hotelDistance && (
                    <small className="text-muted">
                      {hotel.hotelDistance.distance} {hotel.hotelDistance.distanceUnit} from center
                    </small>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Stay Details */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">
                <FaCalendarAlt className="me-2" />
                Stay Details
              </h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="stay-detail">
                    <strong>Check-in:</strong>
                    <p className="mb-2">{formatDate(searchParams?.checkInDate)}</p>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="stay-detail">
                    <strong>Check-out:</strong>
                    <p className="mb-2">{formatDate(searchParams?.checkOutDate)}</p>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="stay-detail">
                    <strong>Duration:</strong>
                    <p className="mb-2">{nights} night{nights !== 1 ? 's' : ''}</p>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="stay-detail">
                    <strong>Guests:</strong>
                    <p className="mb-2">
                      <FaUsers className="me-1" />
                      {guests?.length || searchParams?.adults} guest{(guests?.length || searchParams?.adults) !== 1 ? 's' : ''}
                    </p>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Room Details */}
          {selectedRooms && selectedRooms.length > 0 && (
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">
                  <FaBed className="me-2" />
                  Room Details
                </h5>
              </Card.Header>
              <Card.Body>
                {selectedRooms.map((room, index) => (
                  <div key={index} className="room-booking-detail mb-3">
                    <Row>
                      <Col md={8}>
                        <h6 className="mb-1">
                          {room.room?.description?.text || 'Standard Room'}
                        </h6>
                        <div className="room-info">
                          <Badge bg="secondary" className="me-2">
                            {room.room?.typeEstimated?.category?.replace('_', ' ') || 'Room'}
                          </Badge>
                          {room.room?.typeEstimated?.bedType && (
                            <Badge bg="outline-secondary" className="me-2">
                              {room.room.typeEstimated.bedType} Bed
                            </Badge>
                          )}
                          <Badge bg="outline-secondary">
                            Quantity: {room.quantity || 1}
                          </Badge>
                        </div>
                      </Col>
                      <Col md={4} className="text-end">
                        <div className="room-price">
                          <strong>৳{(parseFloat(room.price?.total || 0) * (room.quantity || 1)).toLocaleString()}</strong>
                          <br />
                          <small className="text-muted">
                            {room.quantity || 1} room{(room.quantity || 1) !== 1 ? 's' : ''} × {nights} night{nights !== 1 ? 's' : ''}
                          </small>
                        </div>
                      </Col>
                    </Row>
                    {index < selectedRooms.length - 1 && <hr />}
                  </div>
                ))}
              </Card.Body>
            </Card>
          )}

          {/* Guest Details */}
          {guests && guests.length > 0 && (
            <Card className="mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">
                  <FaUsers className="me-2" />
                  Guest Details
                </h5>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => setShowFullDetails(!showFullDetails)}
                >
                  {showFullDetails ? 'Hide Details' : 'Show Details'}
                </Button>
              </Card.Header>
              <Card.Body>
                {showFullDetails ? (
                  <Table responsive>
                    <thead>
                      <tr>
                        <th>Guest</th>
                        <th>Name</th>
                        <th>Contact</th>
                        <th>Date of Birth</th>
                      </tr>
                    </thead>
                    <tbody>
                      {guests.map((guest, index) => (
                        <tr key={index}>
                          <td>
                            Guest {index + 1}
                            {index === 0 && <Badge bg="primary" className="ms-2">Main</Badge>}
                          </td>
                          <td>
                            {guest.title} {guest.firstName} {guest.lastName}
                          </td>
                          <td>
                            {index === 0 && (
                              <>
                                <div>{guest.email}</div>
                                <small className="text-muted">{guest.phone}</small>
                              </>
                            )}
                          </td>
                          <td>{guest.dateOfBirth}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                ) : (
                  <p className="mb-0">
                    <strong>Main Guest:</strong> {guests[0]?.title} {guests[0]?.firstName} {guests[0]?.lastName}
                    <br />
                    <small className="text-muted">
                      {guests[0]?.email} • {guests[0]?.phone}
                    </small>
                  </p>
                )}
              </Card.Body>
            </Card>
          )}
        </Col>

        {/* Booking Summary Sidebar */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Booking Summary</h5>
            </Card.Header>
            <Card.Body>
              <div className="booking-summary">
                <div className="d-flex justify-content-between mb-2">
                  <span>Total Amount:</span>
                  <strong className="text-primary">৳{totalPrice.toLocaleString()}</strong>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>Status:</span>
                  <Badge bg="success">Confirmed</Badge>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Booking Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
                
                <hr />
                
                <div className="booking-actions d-grid gap-2">
                  <Button variant="outline-primary" onClick={handlePrint}>
                    <FaPrint className="me-2" />
                    Print Booking
                  </Button>
                  <Button variant="outline-secondary" onClick={handleDownload}>
                    <FaDownload className="me-2" />
                    Download Details
                  </Button>
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Important Information */}
          <Card className="mb-4">
            <Card.Header>
              <h6 className="mb-0">Important Information</h6>
            </Card.Header>
            <Card.Body>
              <Alert variant="info" className="mb-3">
                <small>
                  <strong>Check-in Time:</strong> Usually 3:00 PM<br />
                  <strong>Check-out Time:</strong> Usually 11:00 AM<br />
                  Please contact the hotel directly for specific times.
                </small>
              </Alert>
              
              <Alert variant="warning" className="mb-0">
                <small>
                  <strong>Cancellation Policy:</strong><br />
                  Please review the hotel's cancellation policy. 
                  Contact the hotel directly for any changes or cancellations.
                </small>
              </Alert>
            </Card.Body>
          </Card>

          {/* Action Buttons */}
          <div className="d-grid gap-2">
            <Button variant="primary" onClick={onNewSearch}>
              Book Another Hotel
            </Button>
            <Button variant="outline-secondary" onClick={onViewBookings}>
              View All Bookings
            </Button>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default HotelBookingConfirmation;
