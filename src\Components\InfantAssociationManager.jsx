import React, { useState, useEffect } from "react";
import { validatePassenger } from "../utils/passengerValidation";

const InfantAssociationManager = ({ passengers, onAssociationChange }) => {
  const [associations, setAssociations] = useState({});
  const [validationStatus, setValidationStatus] = useState({});

  // Extract adults and infants from passengers
  const adults = passengers.filter((p) => p.type === "ADULT");
  const infants = passengers.filter((p) => p.type === "INFANT");

  useEffect(() => {
    // Initialize associations from existing data
    const initialAssociations = {};
    infants.forEach((infant) => {
      if (infant.associatedAdult) {
        initialAssociations[infant.id] = infant.associatedAdult;
      }
    });
    setAssociations(initialAssociations);
    validateAssociations(initialAssociations);
  }, [passengers]);

  const validateAssociations = (currentAssociations) => {
    const status = {};
    const adultInfantCount = {};

    // Count infants per adult
    Object.values(currentAssociations).forEach((adultId) => {
      adultInfantCount[adultId] = (adultInfantCount[adultId] || 0) + 1;
    });

    // Validate each infant's association
    infants.forEach((infant) => {
      const adultId = currentAssociations[infant.id];
      const associatedAdult = adults.find((a) => a.id === adultId);

      if (!adultId) {
        status[infant.id] = {
          isValid: false,
          message: "No adult associated",
        };
      } else if (!associatedAdult) {
        status[infant.id] = {
          isValid: false,
          message: "Invalid adult association",
        };
      } else {
        // Check if adult already has an infant
        if (adultInfantCount[adultId] > 1) {
          status[infant.id] = {
            isValid: false,
            message: "Adult already has an infant",
          };
        } else {
          // Verify adult's age
          const adultDob = new Date(associatedAdult.dob);
          const today = new Date();
          const adultAge = Math.floor(
            (today - adultDob) / (1000 * 60 * 60 * 24 * 365.25)
          );

          if (adultAge < 18) {
            status[infant.id] = {
              isValid: false,
              message: "Adult must be at least 18 years old",
            };
          } else {
            status[infant.id] = {
              isValid: true,
              message: "Valid association",
            };
          }
        }
      }
    });

    setValidationStatus(status);
    return Object.values(status).every((s) => s.isValid);
  };

  const handleAssociationChange = (infantId, adultId) => {
    const newAssociations = {
      ...associations,
      [infantId]: adultId,
    };

    setAssociations(newAssociations);
    const isValid = validateAssociations(newAssociations);

    onAssociationChange(infantId, adultId, isValid);
  };

  const getAdultDisplayName = (adult) => {
    if (adult.firstName && adult.lastName) {
      return `${adult.firstName} ${adult.lastName}`;
    }
    return `Adult ${adults.indexOf(adult) + 1}`;
  };

  return (
    <div className="infant-association-manager">
      <h5 className="mb-3">Infant-Adult Associations</h5>

      {infants.length === 0 ? (
        <p className="text-muted">No infants in the booking</p>
      ) : (
        <div className="infant-associations">
          {infants.map((infant) => (
            <div key={infant.id} className="infant-association-item mb-3">
              <div className="card">
                <div className="card-body">
                  <h6 className="card-title">
                    {infant.firstName ||
                      `Infant ${infants.indexOf(infant) + 1}`}
                  </h6>

                  <div className="row align-items-center">
                    <div className="col-md-6">
                      <select
                        className={`form-select ${
                          validationStatus[infant.id]?.isValid === false
                            ? "is-invalid"
                            : validationStatus[infant.id]?.isValid === true
                              ? "is-valid"
                              : ""
                        }`}
                        value={associations[infant.id] || ""}
                        onChange={(e) =>
                          handleAssociationChange(infant.id, e.target.value)
                        }
                      >
                        <option value="">Select accompanying adult</option>
                        {adults.map((adult) => (
                          <option
                            key={adult.id}
                            value={adult.id}
                            disabled={
                              Object.values(associations).includes(adult.id) &&
                              associations[infant.id] !== adult.id
                            }
                          >
                            {getAdultDisplayName(adult)}
                            {Object.values(associations).includes(adult.id) &&
                            associations[infant.id] !== adult.id
                              ? " (Already accompanying an infant)"
                              : ""}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="col-md-6">
                      {validationStatus[infant.id] && (
                        <div
                          className={`validation-message ${validationStatus[infant.id].isValid ? "text-success" : "text-danger"}`}
                        >
                          <i
                            className={`fas ${validationStatus[infant.id].isValid ? "fa-check-circle" : "fa-exclamation-circle"} me-2`}
                          ></i>
                          {validationStatus[infant.id].message}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Additional guidelines */}
                  <div className="mt-3">
                    <small className="text-muted">
                      <i className="fas fa-info-circle me-2"></i>
                      Each infant must be associated with an adult passenger
                      (18+ years old). One adult can only accompany one infant.
                    </small>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default InfantAssociationManager;
