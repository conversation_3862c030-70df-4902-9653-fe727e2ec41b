import React, { useState, useEffect } from 'react';
import '../assets/styles/FlexibleDateSearch.css';
import { searchFlexibleDates } from '../services/apiService';
import { formatPrice } from '../utils/formatUtils';

const FlexibleDateSearch = ({ onFlexibleDateChange, searchParams }) => {
  const [isFlexible, setIsFlexible] = useState(false);
  const [flexDays, setFlexDays] = useState(3); // Default to ±3 days
  const [loading, setLoading] = useState(false);
  const [priceMatrix, setPriceMatrix] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [error, setError] = useState(null);

  // Debug search params changes
  useEffect(() => {
    console.log('Search params updated:', searchParams);
  }, [searchParams]);

  // Handle flexible date toggle
  const handleFlexibleToggle = (e) => {
    const checked = e.target.checked;
    setIsFlexible(checked);
    setError(null);
    
    if (checked) {
      onFlexibleDateChange({ enabled: true, days: flexDays });
      fetchFlexiblePrices(flexDays);
    } else {
      onFlexibleDateChange({ enabled: false, days: 0 });
      setPriceMatrix([]);
      setSelectedDate(null);
    }
  };
  
  // Handle flex days change
  const handleFlexDaysChange = async (e) => {
    const days = parseInt(e.target.value);
    setFlexDays(days);
    setError(null);
    
    if (isFlexible) {
      onFlexibleDateChange({ enabled: true, days });
      await fetchFlexiblePrices(days);
    }
  };

  // Fetch prices for flexible dates
  const fetchFlexiblePrices = async (days) => {
    if (!searchParams?.originLocationCode || !searchParams?.destinationLocationCode || !searchParams?.departureDate) {
      setError('Please select origin, destination, and departure date first');
      return;
    }

    // Ensure we have valid IATA codes
    if (!searchParams.originLocationCode.match(/^[A-Z]{3}$/) || 
        !searchParams.destinationLocationCode.match(/^[A-Z]{3}$/)) {
      setError('Please enter valid airport codes');
      return;
    }

    // Format the departure date if it's a Date object
    const formattedParams = {
      ...searchParams,
      departureDate: typeof searchParams.departureDate === 'object' 
        ? searchParams.departureDate.toISOString().split('T')[0]
        : searchParams.departureDate,
      originLocationCode: searchParams.originLocationCode.toUpperCase(),
      destinationLocationCode: searchParams.destinationLocationCode.toUpperCase(),
      currencyCode: "BDT"  // Ensure currency is always BDT
    };

    setLoading(true);
    setError(null);
    try {
      const results = await searchFlexibleDates(formattedParams, days);
      if (results && results.length > 0) {
        setPriceMatrix(results);
      } else {
        setError('No flights found for the selected dates');
      }
    } catch (error) {
      console.error('Error fetching flexible dates:', error);
      setError('Failed to fetch flight prices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      weekday: 'short'
    }).format(date);
  };

  // Handle date selection from the matrix
  const handleDateSelect = (date, flights) => {
    setSelectedDate(date);
    onFlexibleDateChange({ 
      enabled: true, 
      days: flexDays,
      selectedDate: date,
      availableFlights: flights 
    });
  };
  
  return (
    <div className="flexible-date-container">
      <div className="form-check form-switch">
        <input
          className="form-check-input"
          type="checkbox"
          id="flexibleDates"
          checked={isFlexible}
          onChange={handleFlexibleToggle}
        />
        <label className="form-check-label" htmlFor="flexibleDates">
          Flexible Dates
        </label>
      </div>
      
      {isFlexible && (
        <>
          <div className="flex-days-selector mt-2">
            <label className="form-label">Date range:</label>
            <div className="btn-group w-100" role="group">
              <input
                type="radio"
                className="btn-check"
                name="flexDays"
                id="flex3"
                value="3"
                checked={flexDays === 3}
                onChange={handleFlexDaysChange}
              />
              <label className="btn btn-outline-primary" htmlFor="flex3">
                ±3 days
              </label>
              
              <input
                type="radio"
                className="btn-check"
                name="flexDays"
                id="flex5"
                value="5"
                checked={flexDays === 5}
                onChange={handleFlexDaysChange}
              />
              <label className="btn btn-outline-primary" htmlFor="flex5">
                ±5 days
              </label>
              
              <input
                type="radio"
                className="btn-check"
                name="flexDays"
                id="flex7"
                value="7"
                checked={flexDays === 7}
                onChange={handleFlexDaysChange}
              />
              <label className="btn btn-outline-primary" htmlFor="flex7">
                ±7 days
              </label>
            </div>
          </div>

          {error && (
            <div className="alert alert-danger mt-3">
              <i className="fas fa-exclamation-circle me-2"></i>
              {error}
            </div>
          )}

          {loading ? (
            <div className="text-center mt-3">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2">Searching flexible dates...</p>
            </div>
          ) : priceMatrix.length > 0 && (
            <div className="price-matrix mt-3">
              <h6>Available Prices by Date</h6>
              <div className="matrix-container">
                <div className="matrix-grid">
                  {priceMatrix.map((dayData) => (
                    <div
                      key={dayData.date}
                      className={`matrix-cell ${dayData.date === selectedDate ? 'selected' : ''} 
                                ${dayData.lowestPrice ? 'has-flights' : 'no-flights'}`}
                      onClick={() => dayData.flights && handleDateSelect(dayData.date, dayData.flights)}
                    >
                      <div className="date">{formatDate(dayData.date)}</div>
                      {dayData.lowestPrice ? (
                        <div className="price">{formatPrice(dayData.lowestPrice, "BDT")}</div>
                      ) : (
                        <div className="no-price">No flights</div>
                      )}
                    </div>
                  ))}
                </div>
                <small className="text-muted mt-2 d-block">
                  Click on a date to see available flights. All prices in BDT.
                </small>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FlexibleDateSearch;
