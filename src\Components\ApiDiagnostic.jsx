import React, { useState } from 'react';

const ApiDiagnostic = () => {
  const [diagnosticInfo, setDiagnosticInfo] = useState(null);
  const [isChecking, setIsChecking] = useState(false);

  const runDiagnostic = () => {
    setIsChecking(true);
    
    const info = {
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VITE_AMADEUS_API_KEY: import.meta.env.VITE_AMADEUS_API_KEY ? 'Set' : 'Not Set',
        VITE_AMADEUS_API_SECRET: import.meta.env.VITE_AMADEUS_API_SECRET ? 'Set' : 'Not Set',
        VITE_AMADEUS_API_URL: import.meta.env.VITE_AMADEUS_API_URL || 'Not Set',
      },
      apiKeyLength: import.meta.env.VITE_AMADEUS_API_KEY ? import.meta.env.VITE_AMADEUS_API_KEY.length : 0,
      secretLength: import.meta.env.VITE_AMADEUS_API_SECRET ? import.meta.env.VITE_AMADEUS_API_SECRET.length : 0,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      location: window.location.href,
    };
    
    setDiagnosticInfo(info);
    setIsChecking(false);
  };

  const testApiConnection = async () => {
    setIsChecking(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v1/security/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: import.meta.env.VITE_AMADEUS_API_KEY,
          client_secret: import.meta.env.VITE_AMADEUS_API_SECRET,
        }),
      });

      const result = await response.json();
      
      setDiagnosticInfo(prev => ({
        ...prev,
        apiTest: {
          status: response.status,
          statusText: response.statusText,
          success: response.ok,
          response: result,
          error: response.ok ? null : result,
        }
      }));
    } catch (error) {
      setDiagnosticInfo(prev => ({
        ...prev,
        apiTest: {
          success: false,
          error: error.message,
          stack: error.stack,
        }
      }));
    }
    setIsChecking(false);
  };

  return (
    <div className="container mt-4">
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">🔍 API Diagnostic Tool</h5>
        </div>
        <div className="card-body">
          <div className="mb-3">
            <button 
              className="btn btn-primary me-2" 
              onClick={runDiagnostic}
              disabled={isChecking}
            >
              {isChecking ? 'Checking...' : 'Check Environment'}
            </button>
            <button 
              className="btn btn-secondary" 
              onClick={testApiConnection}
              disabled={isChecking}
            >
              {isChecking ? 'Testing...' : 'Test API Connection'}
            </button>
          </div>

          {diagnosticInfo && (
            <div className="mt-4">
              <h6>Diagnostic Results:</h6>
              <pre className="bg-light p-3 rounded" style={{ fontSize: '12px', overflow: 'auto' }}>
                {JSON.stringify(diagnosticInfo, null, 2)}
              </pre>
              
              <div className="mt-3">
                <h6>Quick Analysis:</h6>
                <ul className="list-unstyled">
                  <li className={diagnosticInfo.environment.VITE_AMADEUS_API_KEY === 'Set' ? 'text-success' : 'text-danger'}>
                    ✓ API Key: {diagnosticInfo.environment.VITE_AMADEUS_API_KEY}
                  </li>
                  <li className={diagnosticInfo.environment.VITE_AMADEUS_API_SECRET === 'Set' ? 'text-success' : 'text-danger'}>
                    ✓ API Secret: {diagnosticInfo.environment.VITE_AMADEUS_API_SECRET}
                  </li>
                  <li className={diagnosticInfo.environment.VITE_AMADEUS_API_URL !== 'Not Set' ? 'text-success' : 'text-warning'}>
                    ✓ API URL: {diagnosticInfo.environment.VITE_AMADEUS_API_URL}
                  </li>
                  {diagnosticInfo.apiTest && (
                    <li className={diagnosticInfo.apiTest.success ? 'text-success' : 'text-danger'}>
                      ✓ API Connection: {diagnosticInfo.apiTest.success ? 'Success' : 'Failed'}
                    </li>
                  )}
                </ul>
              </div>

              {diagnosticInfo.environment.VITE_AMADEUS_API_KEY === 'Not Set' && (
                <div className="alert alert-danger mt-3">
                  <strong>❌ Missing API Key!</strong><br/>
                  Your VITE_AMADEUS_API_KEY environment variable is not set. 
                  This is likely the cause of the "Failed to fetch flight data" error.
                </div>
              )}

              {diagnosticInfo.apiTest && !diagnosticInfo.apiTest.success && (
                <div className="alert alert-danger mt-3">
                  <strong>❌ API Connection Failed!</strong><br/>
                  Error: {diagnosticInfo.apiTest.error}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiDiagnostic;
