.booking-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
}

.passenger-details-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #f5f7fa;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;
}

.flight-details {
  padding: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

/* Booking Summary Styles */
.booking-summary {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: all 0.3s ease;
}

.booking-summary .card-header {
  background-color: var(--primary);
  color: white;
}

.flight-summary-card {
  background-color: white;
  transition: all 0.3s ease;
}

.flight-summary-card:hover {
  background-color: #f8f9fa;
}

.flight-direction-badge {
  font-size: 0.85rem;
  font-weight: 600;
}

.airport-code {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary);
}

.airport-name {
  font-size: 0.95rem;
}

.departure-time,
.arrival-time {
  font-size: 0.85rem;
  color: var(--secondary);
}

.flight-duration {
  font-weight: 600;
  font-size: 0.95rem;
}

.flight-line {
  position: relative;
  height: 2px;
}

.flight-line hr {
  border-top: 2px dashed #ccc;
}

.flight-stops {
  font-size: 0.8rem;
}

.passenger-type-badge {
  background-color: #f8f9fa;
  border-radius: var(--border-radius-md);
  font-size: 0.9rem;
}

.price-summary {
  background-color: #f8f9fa;
  border-radius: var(--border-radius-md);
}

.price-breakdown {
  font-size: 0.95rem;
}

.total-amount {
  color: var(--success);
  font-weight: 700;
}

.fare-features ul li {
  margin-bottom: 0.5rem;
}

.passenger-form,
.payment-form {
  margin: 2rem 0;
}

.passenger-form h3 {
  color: var(--primary);
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 0.75rem;
}

.passenger-card {
  padding: 1.5rem;
  margin: 1.5rem 0;
  background: var(--secondary-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid #e0e0e0;
  transition: var(--transition-base);
}

.passenger-card.has-errors {
  border-color: #dc3545;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.1);
}

.passenger-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem 0.5rem 0 0;
}

.passenger-card .card-body {
  padding: 1.5rem;
}

.passenger-number {
  background-color: #e9ecef;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  color: #495057;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--dark);
  font-size: 0.95rem;
}

.form-control,
.form-select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #dc3545;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

.form-control.is-invalid:focus,
.form-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #dc3545;
}

.auto-save-indicator {
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #6c757d;
}

.auto-save-indicator i {
  margin-right: 0.5rem;
  color: #28a745;
}

/* Enhanced validation states */
.passenger-form-fields {
  position: relative;
}

.passenger-form-fields .form-control:valid,
.passenger-form-fields .form-select:valid {
  border-color: #198754;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

/* Infant-specific styles */
.infant-association {
  background-color: #e9ecef;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-top: 1rem;
}

.infant-association select option:disabled {
  color: #6c757d;
  font-style: italic;
}

/* Special requirements section */
.special-requirements {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .passenger-details-container {
    padding: 1rem;
  }

  .passenger-card {
    margin-bottom: 1.5rem;
  }

  .passenger-card .card-header {
    padding: 1rem;
  }

  .passenger-card .card-body {
    padding: 1rem;
  }

  .form-row {
    margin-bottom: 1rem;
  }

  .passenger-form h3 {
    font-size: 1.5rem;
  }
}
