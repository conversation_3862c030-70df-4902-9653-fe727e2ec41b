/* DatePicker.css - Custom styles for the enhanced date picker */

/* DatePicker Container */
.datepicker-container {
  position: relative;
  height: 100%;
}

.datepicker-wrapper {
  position: relative;
  height: 100%;
}

/* Input Field */
.datepicker-wrapper .form-control {
  height: 48px;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  font-size: 0.95rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  width: 100%;
  background: white;
}

.datepicker-wrapper .form-control:hover {
  border-color: #3b82f6;
}

.datepicker-wrapper .form-control:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Date Range Picker Specific */
.date-range-picker-container {
  height: 100%;
}

.date-range-picker-container .row {
  height: 100%;
  margin: 0 -0.5rem;
}

.date-range-picker-container .col-md-6 {
  height: 100%;
  padding: 0 0.5rem;
}

/* Calendar icon */
.datepicker-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s ease;
  z-index: 2;
  padding: 8px;
}

/* Form Label */
.form-label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  display: block;
}

/* Calendar popup */
.react-datepicker-popper {
  z-index: 1000;
}

.custom-datepicker {
  font-family: inherit;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 1.25rem;
  margin-top: 0.5rem;
}

/* Header styling */
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.month-year {
  font-weight: 600;
  font-size: 1.1rem;
  color: #1e293b;
}

.navigation-button {
  background: #f8fafc;
  color: #1e293b;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.navigation-button:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
}

.navigation-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

/* Calendar days */
.react-datepicker__month-container {
  margin: 0;
}

.react-datepicker__day-names {
  display: flex;
  justify-content: space-around;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 0.5rem;
}

.react-datepicker__day-name {
  color: #64748b;
  width: 2.5rem;
  line-height: 2.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

.react-datepicker__day {
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  border-radius: 8px;
  margin: 0.2rem;
  color: #1e293b;
  transition: all 0.2s ease;
  position: relative;
}

.react-datepicker__day:hover:not(.react-datepicker__day--disabled) {
  background-color: #f1f5f9;
  color: #3b82f6;
}

.react-datepicker__day--selected {
  background: #3b82f6 !important;
  color: white !important;
  font-weight: 600;
}

.react-datepicker__day--in-range {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background: #3b82f6 !important;
  color: white !important;
  font-weight: 600;
}

.react-datepicker__day--keyboard-selected {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.react-datepicker__day--disabled {
  color: #cbd5e1;
  cursor: not-allowed;
}

/* Today's date */
.highlighted-today {
  border: 2px solid #3b82f6;
  font-weight: 600;
}

/* Weekend days */
.weekend-day {
  color: #3b82f6;
}

/* Custom day contents */
.custom-day-contents {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.custom-day-contents .price {
  font-size: 0.75rem;
  color: #10b981;
  margin-top: 2px;
}

/* Dark theme support */
.dark-theme {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2);
}

.dark-theme .custom-header {
  border-bottom-color: #334155;
}

.dark-theme .react-datepicker__day-names {
  border-bottom-color: #334155;
}

.dark-theme .react-datepicker__day {
  color: #f1f5f9;
}

.dark-theme .react-datepicker__day:hover:not(.react-datepicker__day--disabled) {
  background-color: #334155;
}

.dark-theme .react-datepicker__day-name {
  color: #94a3b8;
}

.dark-theme .month-year {
  color: #f1f5f9;
}

.dark-theme .navigation-button {
  background: #334155;
  color: #f1f5f9;
}

.dark-theme .navigation-button:hover:not(:disabled) {
  background: #3b82f6;
}

.dark-theme .react-datepicker__day--in-range {
  background-color: rgba(59, 130, 246, 0.2);
}

.dark-theme .react-datepicker__day--disabled {
  color: #475569;
}

/* Error States */
.datepicker-wrapper.is-invalid .form-control {
  border-color: #ef4444;
}

.datepicker-wrapper.is-invalid .datepicker-icon {
  color: #ef4444;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .react-datepicker-popper {
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    transform: none !important;
    margin: 0;
    padding: 0;
  }

  .custom-datepicker {
    border-radius: 20px 20px 0 0;
    animation: slideUp 0.3s ease;
    padding: 1rem;
    margin-top: 0;
  }

  .react-datepicker__day,
  .react-datepicker__day-name {
    width: 2.25rem;
    height: 2.25rem;
    line-height: 2.25rem;
  }

  .date-range-picker-container .row {
    margin: 0 -0.5rem;
  }

  .date-range-picker-container [class*="col-"] {
    padding: 0 0.5rem;
  }

  .datepicker-wrapper .form-control {
    font-size: 0.875rem;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
