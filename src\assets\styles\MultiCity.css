.multi-city-container {
  margin-bottom: 1.5rem;
}

.multi-city-segment {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 1.25rem;
  position: relative;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.multi-city-segment:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.segment-header h5 {
  color: #1e293b;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.segment-header h5 i {
  color: #3b82f6;
}

.segment-remove-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.segment-remove-btn:hover {
  background-color: #fee2e2;
  border-color: #ef4444;
  color: #dc2626;
}

.arrow-icon,
.date-separator {
  width: 32px;
  height: 48px;
  color: #3b82f6;
  display: flex;
  justify-content: center;
  align-items: center;
}

.field-group {
  margin-bottom: 0;
}

.field-group .form-label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  white-space: nowrap;
}

.add-segment-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: #ffffff;
  border: 2px solid #3b82f6;
  color: #3b82f6;
}

.add-segment-button:hover {
  background: #3b82f6;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.add-segment-button i {
  margin-right: 0.5rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .multi-city-segment {
    background-color: #1e293b;
    border-color: #334155;
  }

  .multi-city-segment:hover {
    border-color: #60a5fa;
    background: #1e293b;
  }

  .segment-header {
    border-bottom-color: #334155;
  }

  .segment-header h5 {
    color: #e2e8f0;
  }

  .segment-header h5 i {
    color: #60a5fa;
  }

  .segment-remove-btn:hover {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
    color: #f87171;
  }

  .arrow-icon,
  .date-separator {
    color: #60a5fa;
  }

  .add-segment-button {
    background: #1e293b;
    border-color: #60a5fa;
    color: #60a5fa;
  }

  .add-segment-button:hover {
    background: #60a5fa;
    color: #1e293b;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .multi-city-segment {
    padding: 1rem;
  }

  .row.g-3 {
    flex-direction: column;
  }

  .col-md-3,
  .col-md-4 {
    width: 100%;
  }

  .arrow-icon,
  .date-separator {
    height: 32px;
    margin: 0.5rem 0;
  }

  .arrow-icon i {
    transform: rotate(90deg);
  }

  .segment-header {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .segment-remove-btn {
    width: 100%;
    justify-content: center;
  }
}
