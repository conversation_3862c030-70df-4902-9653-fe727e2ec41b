import React, { useState, useEffect } from "react";
import ReactDatePicker from "react-datepicker";
import { useAppContext } from "../context/AppContext";
import "react-datepicker/dist/react-datepicker.css";
import "../assets/styles/DatePicker.css";

const DateRangePicker = ({
  startDateName,
  endDateName,
  startDate,
  endDate,
  onChange,
  startDateLabel = "Departure Date",
  endDateLabel = "Return Date",
  minDate,
  isStartDateInvalid,
  isEndDateInvalid,
  startDateErrorMessage,
  endDateErrorMessage,
  required = false,
}) => {
  const { state } = useAppContext();
  const isDarkMode = state.theme === "dark";

  const [selectedStartDate, setSelectedStartDate] = useState(
    startDate
      ? typeof startDate === "string"
        ? new Date(startDate)
        : startDate
      : null
  );

  const [selectedEndDate, setSelectedEndDate] = useState(
    endDate ? (typeof endDate === "string" ? new Date(endDate) : endDate) : null
  );

  // Track calendar open states
  const [isStartCalendarOpen, setIsStartCalendarOpen] = useState(false);
  const [isEndCalendarOpen, setIsEndCalendarOpen] = useState(false);

  useEffect(() => {
    if (startDate) {
      setSelectedStartDate(
        typeof startDate === "string" ? new Date(startDate) : startDate
      );
    } else {
      setSelectedStartDate(null);
    }

    if (endDate) {
      setSelectedEndDate(
        typeof endDate === "string" ? new Date(endDate) : endDate
      );
    } else {
      setSelectedEndDate(null);
    }
  }, [startDate, endDate]);

  const handleStartDateChange = (date) => {
    setSelectedStartDate(date);
    setIsStartCalendarOpen(false);

    const formattedDate = date
      ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
          .toISOString()
          .split("T")[0]
      : "";
    onChange({
      target: {
        name: startDateName,
        value: formattedDate,
      },
    });

    if (selectedEndDate && date && selectedEndDate < date) {
      const newEndDate = new Date(date);
      newEndDate.setDate(newEndDate.getDate() + 1);
      setSelectedEndDate(newEndDate);
      onChange({
        target: {
          name: endDateName,
          value: new Date(
            newEndDate.getTime() - newEndDate.getTimezoneOffset() * 60000
          )
            .toISOString()
            .split("T")[0],
        },
      });
    }
  };

  const handleEndDateChange = (date) => {
    setSelectedEndDate(date);
    setIsEndCalendarOpen(false);

    const formattedDate = date
      ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
          .toISOString()
          .split("T")[0]
      : "";
    onChange({
      target: {
        name: endDateName,
        value: formattedDate,
      },
    });
  };

  const getDayClassNames = (date) => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;

    return `${isToday ? "highlighted-today" : ""} ${isWeekend ? "weekend-day" : ""}`.trim();
  };

  const CustomHeader = ({
    date,
    decreaseMonth,
    increaseMonth,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
  }) => (
    <div className="custom-header">
      <button
        type="button"
        onClick={decreaseMonth}
        disabled={prevMonthButtonDisabled}
        className="navigation-button"
        aria-label="Previous month"
      >
        <i className="fas fa-chevron-left"></i>
      </button>
      <div className="month-year">
        {date.toLocaleString("default", { month: "long", year: "numeric" })}
      </div>
      <button
        type="button"
        onClick={increaseMonth}
        disabled={nextMonthButtonDisabled}
        className="navigation-button"
        aria-label="Next month"
      >
        <i className="fas fa-chevron-right"></i>
      </button>
    </div>
  );

  const renderDayContents = (day, date) => {
    return (
      <div className="custom-day-contents">
        <span>{day}</span>
      </div>
    );
  };

  return (
    <div className="date-range-picker-container">
      <div className="row g-0">
        <div className="col-md-6 pe-md-2">
          <div className="datepicker-container">
            <label
              className="form-label fw-bold"
              htmlFor={`datepicker-${startDateName}`}
            >
              {startDateLabel}
              {required && <span className="text-danger ms-1">*</span>}
            </label>
            <div
              className={`datepicker-wrapper ${isStartDateInvalid ? "is-invalid" : ""}`}
            >
              <ReactDatePicker
                id={`datepicker-${startDateName}`}
                selected={selectedStartDate}
                onChange={handleStartDateChange}
                selectsStart
                startDate={selectedStartDate}
                endDate={selectedEndDate}
                minDate={
                  minDate
                    ? typeof minDate === "string"
                      ? new Date(minDate)
                      : minDate
                    : new Date()
                }
                placeholderText="Select departure date"
                className={`form-control ${isStartDateInvalid ? "is-invalid" : ""}`}
                calendarClassName={`custom-datepicker ${isDarkMode ? "dark-theme" : ""}`}
                dayClassName={getDayClassNames}
                renderDayContents={renderDayContents}
                renderCustomHeader={CustomHeader}
                open={isStartCalendarOpen}
                onClickOutside={() => setIsStartCalendarOpen(false)}
                onInputClick={() => setIsStartCalendarOpen(true)}
                showPopperArrow={false}
                dateFormat="MMM d, yyyy"
                popperModifiers={[
                  {
                    name: "offset",
                    options: {
                      offset: [0, 8],
                    },
                  },
                  {
                    name: "preventOverflow",
                    options: {
                      rootBoundary: "viewport",
                      padding: 8,
                    },
                  },
                ]}
              />
              <div
                className="datepicker-icon"
                onClick={() => setIsStartCalendarOpen(!isStartCalendarOpen)}
                role="button"
                aria-label="Toggle departure date calendar"
              >
                <i className="fas fa-plane-departure"></i>
              </div>
            </div>
            {isStartDateInvalid && startDateErrorMessage && (
              <div className="invalid-feedback d-block">
                {startDateErrorMessage}
              </div>
            )}
          </div>
        </div>

        <div className="col-md-6 ps-md-2">
          <div className="datepicker-container">
            <label
              className="form-label fw-bold"
              htmlFor={`datepicker-${endDateName}`}
            >
              {endDateLabel}
              {required && <span className="text-danger ms-1">*</span>}
            </label>
            <div
              className={`datepicker-wrapper ${isEndDateInvalid ? "is-invalid" : ""}`}
            >
              <ReactDatePicker
                id={`datepicker-${endDateName}`}
                selected={selectedEndDate}
                onChange={handleEndDateChange}
                selectsEnd
                startDate={selectedStartDate}
                endDate={selectedEndDate}
                minDate={selectedStartDate || new Date()}
                placeholderText="Select return date"
                className={`form-control ${isEndDateInvalid ? "is-invalid" : ""}`}
                calendarClassName={`custom-datepicker ${isDarkMode ? "dark-theme" : ""}`}
                dayClassName={getDayClassNames}
                renderDayContents={renderDayContents}
                renderCustomHeader={CustomHeader}
                open={isEndCalendarOpen}
                onClickOutside={() => setIsEndCalendarOpen(false)}
                onInputClick={() => setIsEndCalendarOpen(true)}
                showPopperArrow={false}
                dateFormat="MMM d, yyyy"
                popperModifiers={[
                  {
                    name: "offset",
                    options: {
                      offset: [0, 8],
                    },
                  },
                  {
                    name: "preventOverflow",
                    options: {
                      rootBoundary: "viewport",
                      padding: 8,
                    },
                  },
                ]}
              />
              <div
                className="datepicker-icon"
                onClick={() => setIsEndCalendarOpen(!isEndCalendarOpen)}
                role="button"
                aria-label="Toggle return date calendar"
              >
                <i className="fas fa-plane-arrival"></i>
              </div>
            </div>
            {isEndDateInvalid && endDateErrorMessage && (
              <div className="invalid-feedback d-block">
                {endDateErrorMessage}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateRangePicker;
