/**
 * Utility functions for seat selection and suggestions
 */

/**
 * Calculates seat suggestions based on passenger type and preferences
 * @param {Object} passenger - Passenger information
 * @param {Array} seatMap - Current seat map
 * @param {Object} selectedSeats - Currently selected seats
 * @returns {Array} - Array of suggested seat numbers
 */
export const getSeatSuggestions = (passenger, seatMap, selectedSeats) => {
  const suggestions = [];

  // Handle infant-associated passengers
  if (passenger.hasInfant) {
    return findInfantFriendlySeats(seatMap, selectedSeats);
  }

  // Handle elderly or passengers with reduced mobility
  if (passenger.specialAssistance) {
    return findAccessibleSeats(seatMap, selectedSeats);
  }

  // Handle family groups (adjacent seats)
  if (passenger.groupId) {
    return findGroupSeats(seatMap, selectedSeats, passenger.groupId);
  }

  // Default suggestions based on cabin class
  return findPreferredSeats(seatMap, selectedSeats, passenger.cabinClass);
};

/**
 * Finds seats suitable for passengers with infants
 */
const findInfantFriendlySeats = (seatMap, selectedSeats) => {
  const suggestions = [];

  seatMap.forEach((row, rowIndex) => {
    row.forEach((seat, seatIndex) => {
      if (
        seat.hasBassinetLocation &&
        !isOccupied(rowIndex, seatIndex, selectedSeats)
      ) {
        suggestions.push({ row: rowIndex, seat: seatIndex, priority: 1 });
      }
      // Add bulkhead seats as secondary options
      if (seat.isBulkhead && !isOccupied(rowIndex, seatIndex, selectedSeats)) {
        suggestions.push({ row: rowIndex, seat: seatIndex, priority: 2 });
      }
    });
  });

  return sortSuggestionsByPriority(suggestions);
};

/**
 * Finds accessible seats for elderly or reduced mobility passengers
 */
const findAccessibleSeats = (seatMap, selectedSeats) => {
  const suggestions = [];

  seatMap.forEach((row, rowIndex) => {
    row.forEach((seat, seatIndex) => {
      if (seat.isAisle && !isOccupied(rowIndex, seatIndex, selectedSeats)) {
        suggestions.push({
          row: rowIndex,
          seat: seatIndex,
          priority: seat.isNearExit ? 1 : 2,
        });
      }
    });
  });

  return sortSuggestionsByPriority(suggestions);
};

/**
 * Finds adjacent seats for groups/families
 */
const findGroupSeats = (seatMap, selectedSeats, groupId) => {
  const suggestions = [];
  const groupSize = getGroupSize(groupId, selectedSeats);

  seatMap.forEach((row, rowIndex) => {
    let consecutiveSeats = 0;
    row.forEach((seat, seatIndex) => {
      if (!isOccupied(rowIndex, seatIndex, selectedSeats)) {
        consecutiveSeats++;
        if (consecutiveSeats >= groupSize) {
          suggestions.push({
            row: rowIndex,
            startSeat: seatIndex - groupSize + 1,
            priority: 1,
          });
        }
      } else {
        consecutiveSeats = 0;
      }
    });
  });

  return sortSuggestionsByPriority(suggestions);
};

/**
 * Finds preferred seats based on cabin class
 */
const findPreferredSeats = (seatMap, selectedSeats, cabinClass) => {
  const suggestions = [];

  seatMap.forEach((row, rowIndex) => {
    row.forEach((seat, seatIndex) => {
      if (!isOccupied(rowIndex, seatIndex, selectedSeats)) {
        let priority = calculateSeatPriority(seat, cabinClass);
        if (priority > 0) {
          suggestions.push({ row: rowIndex, seat: seatIndex, priority });
        }
      }
    });
  });

  return sortSuggestionsByPriority(suggestions);
};

/**
 * Helper function to check if a seat is occupied
 */
const isOccupied = (row, seat, selectedSeats) => {
  return selectedSeats.some(
    (selected) => selected.row === row && selected.seat === seat
  );
};

/**
 * Helper function to calculate seat priority based on cabin class and features
 */
const calculateSeatPriority = (seat, cabinClass) => {
  let priority = 0;

  if (cabinClass === "premium" && seat.isPremium) priority += 3;
  if (seat.isWindow) priority += 2;
  if (seat.isAisle) priority += 2;
  if (seat.isExit) priority += 1;

  return priority;
};

/**
 * Helper function to sort suggestions by priority
 */
const sortSuggestionsByPriority = (suggestions) => {
  return suggestions.sort((a, b) => b.priority - a.priority);
};

/**
 * Helper function to get group size
 */
const getGroupSize = (groupId, selectedSeats) => {
  return selectedSeats.filter((seat) => seat.groupId === groupId).length;
};

/**
 * Smart seat suggestion utility function
 * @param {Array} seatMap - Current seat map
 * @param {number} passengerCount - Number of passengers
 * @param {Object} preferences - Passenger preferences
 * @returns {Array} - Array of suggested seat numbers
 */
export const getSuggestedSeats = (
  seatMap,
  passengerCount,
  preferences = {}
) => {
  const availableSeats = seatMap
    .flat()
    .filter((seat) => seat && !seat.occupied);
  const suggestions = [];

  // Sort seats based on preferences
  const sortedSeats = availableSeats.sort((a, b) => {
    let scoreA = 0;
    let scoreB = 0;

    // Prefer window or aisle based on preference
    if (preferences.seatType === "window" && a.position === "window")
      scoreA += 3;
    if (preferences.seatType === "window" && b.position === "window")
      scoreB += 3;
    if (preferences.seatType === "aisle" && a.position === "aisle") scoreA += 3;
    if (preferences.seatType === "aisle" && b.position === "aisle") scoreB += 3;

    // Prefer seats in the middle of the aircraft for less turbulence
    const midPoint = Math.floor(seatMap.length / 2);
    const distanceFromMiddleA = Math.abs(a.row - midPoint);
    const distanceFromMiddleB = Math.abs(b.row - midPoint);
    scoreA -= distanceFromMiddleA;
    scoreB -= distanceFromMiddleB;

    // Prefer seats together for groups
    if (passengerCount > 1) {
      const adjacentSeatsA = getAdjacentAvailableSeats(
        seatMap,
        a,
        passengerCount
      );
      const adjacentSeatsB = getAdjacentAvailableSeats(
        seatMap,
        b,
        passengerCount
      );
      if (adjacentSeatsA >= passengerCount) scoreA += 5;
      if (adjacentSeatsB >= passengerCount) scoreB += 5;
    }

    return scoreB - scoreA;
  });

  // Get top suggestions
  return sortedSeats.slice(0, passengerCount);
};

const getAdjacentAvailableSeats = (seatMap, seat, count) => {
  const row = seatMap[seat.row];
  let adjacent = 1;
  let left = seat.col - 1;
  let right = seat.col + 1;

  while ((left >= 0 || right < row.length) && adjacent < count) {
    if (left >= 0 && row[left] && !row[left].occupied) {
      adjacent++;
      left--;
    }
    if (right < row.length && row[right] && !row[right].occupied) {
      adjacent++;
      right++;
    }
    if (left < 0 && right >= row.length) break;
  }

  return adjacent;
};
