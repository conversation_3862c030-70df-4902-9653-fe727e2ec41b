.filter-panel {
  background-color: white;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  border: 2px solid rgba(142, 202, 230, 0.3);
  box-shadow: 0 10px 30px rgba(2, 48, 71, 0.1);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--accent-yellow);
}

.filter-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--primary-dark);
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section h6 {
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-dark);
}

.filter-options {
  max-height: 150px;
  overflow-y: auto;
  padding-right: 5px;
}

.form-check {
  margin-bottom: 8px;
}

.form-check-label {
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.duration-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

@media (max-width: 768px) {
  .filter-panel {
    padding: 10px;
  }

  .filter-options {
    max-height: 120px;
  }
}
