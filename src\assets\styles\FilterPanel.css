.filter-panel {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6;
}

.filter-header h5 {
  margin: 0;
  font-weight: 600;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section h6 {
  font-weight: 600;
  margin-bottom: 10px;
  color: #495057;
}

.filter-options {
  max-height: 150px;
  overflow-y: auto;
  padding-right: 5px;
}

.form-check {
  margin-bottom: 8px;
}

.form-check-label {
  font-size: 0.9rem;
  color: #495057;
}

.duration-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

@media (max-width: 768px) {
  .filter-panel {
    padding: 10px;
  }
  
  .filter-options {
    max-height: 120px;
  }
}
