import axios from 'axios';

// Amadeus API credentials from environment variables
const AMADEUS_API_KEY = import.meta.env.VITE_AMADEUS_API_KEY || '********************************';
const AMADEUS_API_SECRET = import.meta.env.VITE_AMADEUS_API_SECRET || 'qZcGFsMJgCv52UkG';
const AMADEUS_API_URL = import.meta.env.VITE_AMADEUS_API_URL || 'https://test.api.amadeus.com';

// Token cache
let tokenData = {
  token: null,
  expiresAt: null
};

/**
 * Get an access token for the Amadeus API
 * @returns {Promise<string>} Access token
 */
const getAccessToken = async () => {
  // Check if we have a valid token
  if (tokenData.token && tokenData.expiresAt && tokenData.expiresAt > Date.now()) {
    return tokenData.token;
  }

  // Get a new token
  try {
    const response = await axios.post(
      `${AMADEUS_API_URL}/v1/security/oauth2/token`,
      new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: AMADEUS_API_KEY,
        client_secret: AMADEUS_API_SECRET,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    // Calculate expiration time (subtract 5 minutes to be safe)
    const expiresIn = response.data.expires_in * 1000; // Convert to milliseconds
    const expiresAt = Date.now() + expiresIn - 300000; // Subtract 5 minutes

    // Cache the token
    tokenData = {
      token: response.data.access_token,
      expiresAt
    };

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting Amadeus access token:', error);
    throw error;
  }
};

/**
 * Search for locations (airports/cities) using the Amadeus API
 * @param {string} keyword - Search keyword
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array<{ id: string, code: string, name: string, city: string, country: string, type: string }>>} Array of location objects
 */
export const searchLocations = async (keyword, limit = 10) => {
  if (!keyword || keyword.length < 1) {
    return [];
  }

  try {
    const token = await getAccessToken();

    const response = await axios.get(
      `${AMADEUS_API_URL}/v1/reference-data/locations`,
      {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          keyword,
          subType: 'AIRPORT,CITY',
          'page[limit]': limit,
          view: 'LIGHT'
        }
      }
    );

    // Transform the response to a more usable format
    return response.data.data.map(location => ({
      id: location.id,
      code: location.iataCode,
      name: location.name,
      city: location.address?.cityName || location.name,
      country: location.address?.countryName || '',
      type: location.subType
    }));
  } catch (error) {
    console.error('Error searching locations:', error);
    return [];
  }
};

export default {
  searchLocations
};
