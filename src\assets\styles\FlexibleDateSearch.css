.flexible-date-container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  border: 1px solid #e9ecef;
}

.form-check-input {
  cursor: pointer;
}

.form-check-label {
  font-weight: 500;
  cursor: pointer;
}

.flex-days-selector {
  padding-top: 10px;
  border-top: 1px dashed #dee2e6;
}

.flex-days-selector .form-label {
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.text-muted {
  font-size: 0.8rem;
}

.price-matrix {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.matrix-container {
  overflow-x: auto;
  padding: 10px 0;
}

.matrix-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.matrix-cell {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.matrix-cell:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.matrix-cell.selected {
  background: #e7f5ff;
  border-color: #339af0;
}

.matrix-cell.has-flights {
  background: #f8f9fa;
}

.matrix-cell.no-flights {
  background: #f1f3f5;
  cursor: not-allowed;
  opacity: 0.7;
}

.matrix-cell .date {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
}

.matrix-cell .price {
  font-size: 0.8rem;
  color: #0ca678;
  margin-top: 4px;
}

.matrix-cell .no-price {
  font-size: 0.8rem;
  color: #adb5bd;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .flexible-date-container {
    padding: 10px;
  }
  
  .matrix-grid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }
  
  .matrix-cell {
    min-width: 80px;
    padding: 6px;
  }
  
  .matrix-cell .date {
    font-size: 0.8rem;
  }
  
  .matrix-cell .price,
  .matrix-cell .no-price {
    font-size: 0.7rem;
  }
}
