# 🌟 Tripstar - Modern Flight Booking Platform

[![React](https://img.shields.io/badge/React-18.0+-blue.svg)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-6.0+-purple.svg)](https://vitejs.dev/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-5.0+-purple.svg)](https://getbootstrap.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

A modern, feature-rich flight booking platform built with React and integrated with Amadeus API. Tripstar offers a seamless travel booking experience with advanced search capabilities, baggage management, seat selection, and a beautiful user interface.

## ✨ Features

### 🛫 **Flight Booking**

- **Multi-Trip Support**: One-way, round-trip, and multi-city bookings
- **Advanced Search**: Flexible date search with price comparison
- **Real-time Results**: Live flight data from Amadeus API
- **Smart Filtering**: Filter by airlines, stops, departure/arrival times
- **Price Comparison**: Best deals with transparent pricing

### 🧳 **Baggage Management**

- **Flexible Options**: Multiple baggage allowances (15kg to 40kg)
- **Per-Passenger Selection**: Individual baggage choices
- **Transparent Pricing**: Clear cost breakdown
- **Flight-Specific**: Different selections for outbound/return flights

### 💺 **Seat Selection**

- **Interactive Seat Maps**: Visual seat selection interface
- **Optional Selection**: Skip seat selection if preferred
- **Side-by-Side Layout**: Outbound and return flights displayed together
- **Real-time Availability**: Live seat availability updates

### 🏨 **Additional Services**

- **Hotel Booking**: Integrated accommodation search
- **Transportation**: Ground transport options
- **Tour Packages**: Complete travel experiences
- **Multi-Currency**: Support for multiple currencies

### 🎨 **Modern Design**

- **Beautiful UI**: Custom color palette with modern aesthetics
- **Responsive Design**: Perfect on desktop, tablet, and mobile
- **Accessibility**: WCAG compliant with screen reader support
- **Progressive Web App**: Installable with offline capabilities

## 🚀 Quick Start

### Prerequisites

- Node.js 18.0 or higher
- npm or yarn package manager
- Amadeus API credentials (for flight data)

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/Roman01-info/tripthree.git
   cd tripthree
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env` file in the root directory:

   ```env
   VITE_AMADEUS_API_KEY=your_amadeus_api_key
   VITE_AMADEUS_API_SECRET=your_amadeus_api_secret
   VITE_AMADEUS_API_URL=https://test.api.amadeus.com
   ```

4. **Start development server**

   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 🛠️ Technology Stack

### Frontend

- **React 18** - Modern UI library with hooks
- **Vite** - Fast build tool and development server
- **Bootstrap 5** - Responsive CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client for API requests

### APIs & Services

- **Amadeus API** - Flight search and booking data
- **RESTful Architecture** - Clean API integration
- **Real-time Data** - Live flight information

### Development Tools

- **ESLint** - Code linting and quality
- **Prettier** - Code formatting
- **Vite HMR** - Hot module replacement
- **Modern JavaScript** - ES6+ features

## 📁 Project Structure

```
tripstar-v3/
├── public/                 # Static assets
├── src/
│   ├── assets/            # Styles, images, fonts
│   │   └── styles/        # CSS files
│   ├── components/        # Reusable UI components
│   ├── features/          # Feature-specific components
│   │   ├── booking/       # Booking flow components
│   │   └── flight-search/ # Flight search functionality
│   ├── pages/             # Page components
│   ├── services/          # API services and utilities
│   ├── utils/             # Helper functions
│   └── App.jsx           # Main application component
├── package.json          # Dependencies and scripts
└── README.md            # Project documentation
```

## 🎨 Design System

### Color Palette

- **Primary Light**: `#8ecae6` - Backgrounds and subtle accents
- **Primary Medium**: `#219ebc` - Buttons and highlights
- **Primary Dark**: `#023047` - Headers and text
- **Accent Yellow**: `#ffb703` - Call-to-action elements
- **Accent Orange**: `#fb8500` - Gradients and hover effects

### Key Design Principles

- **Modern Aesthetics** - Clean, contemporary design
- **User-Centric** - Intuitive navigation and interactions
- **Accessibility First** - WCAG 2.1 AA compliance
- **Mobile Responsive** - Optimized for all screen sizes

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier

# Testing
npm run test         # Run test suite
npm run test:watch   # Run tests in watch mode
```

## 🌐 API Integration

### Amadeus API

The application integrates with Amadeus API for:

- Flight search and availability
- Real-time pricing
- Airport and airline data
- Booking capabilities

### Configuration

Set up your Amadeus API credentials in the environment variables:

- `VITE_AMADEUS_API_KEY` - Your API key
- `VITE_AMADEUS_API_SECRET` - Your API secret
- `VITE_AMADEUS_API_URL` - API endpoint URL

## 📱 Progressive Web App

Tripstar is built as a PWA with:

- **Offline Support** - Basic functionality without internet
- **Installable** - Add to home screen on mobile devices
- **Fast Loading** - Optimized performance and caching
- **Responsive** - Works perfectly on all devices

## 🔒 Security Features

- **Environment Variables** - Secure API key management
- **Input Validation** - Comprehensive form validation
- **Error Handling** - Graceful error management
- **HTTPS Ready** - SSL/TLS support for production

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

### Deploy to Vercel

```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify

```bash
npm run build
# Upload dist/ folder to Netlify
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Amadeus API** - For providing comprehensive travel data
- **React Community** - For the amazing ecosystem
- **Bootstrap Team** - For the responsive framework
- **Contributors** - Thank you to all who have contributed

## 📞 Support

For support and questions:

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/Roman01-info/tripthree/issues)
- 📖 Documentation: [Wiki](https://github.com/Roman01-info/tripthree/wiki)

---

<div align="center">
  <p>Made with ❤️ by the Tripstar Team</p>
  <p>
    <a href="https://github.com/Roman01-info/tripthree">⭐ Star this repo</a> •
    <a href="https://github.com/Roman01-info/tripthree/issues">🐛 Report Bug</a> •
    <a href="https://github.com/Roman01-info/tripthree/issues">✨ Request Feature</a>
  </p>
</div>
