/* Import hotel components styles */
@import "./assets/styles/HotelComponents.css";

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* color-scheme: light dark; */
  /* color: rgba(255, 255, 255, 0.87); */
  /* background-color: #242424; */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar height */
}

/* Ensure all page containers work with fixed navbar */
.container-fluid,
.page-container {
  padding-top: 1rem;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Adjust for fixed navbar when using anchor links */
:target {
  scroll-margin-top: 100px;
}
