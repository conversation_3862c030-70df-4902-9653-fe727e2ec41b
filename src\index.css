/* Import hotel components styles */
@import "./assets/styles/HotelComponents.css";

:root {
  /* Global Color Palette - Matching Homepage Design */
  --primary-light: #8ecae6; /* Light blue */
  --primary-medium: #219ebc; /* Medium blue */
  --primary-dark: #023047; /* Dark blue */
  --accent-yellow: #ffb703; /* Yellow */
  --accent-orange: #fb8500; /* Orange */

  /* Additional colors for better design */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;
  --shadow: rgba(2, 48, 71, 0.1);
  --shadow-hover: rgba(2, 48, 71, 0.2);

  /* Typography */
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar height */
}

/* Ensure all page containers work with fixed navbar */
.container-fluid,
.page-container {
  padding-top: 1rem;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Adjust for fixed navbar when using anchor links */
:target {
  scroll-margin-top: 100px;
}
