import React from "react";
import { formatAirportDisplay } from "../utils/airportUtils";
import { getAirlineByCode } from "../utils/airlineUtils";
import "../assets/styles/flightSearch.css";

/**
 * Shared component for displaying flight information
 * Used by both FlightResult and ReturnFlightResult
 */
const FlightItem = ({
  flight,
  onSelect,
  isSelected,
  buttonText = "Select",
  selectedButtonText = "Selected",
}) => {
  // We're now using the imported getAirlineByCode function from airlineUtils.js

  // Get transit count
  const getTransitCount = (flight) => {
    if (!flight.itineraries?.[0]?.segments) return 0;
    return flight.itineraries[0].segments.length - 1;
  };

  // Get transit info
  const getTransitInfo = (flight) => {
    if (!flight.itineraries?.[0]?.segments) return [];
    const segments = flight.itineraries[0].segments;
    return segments.slice(1).map((segment) => {
      const iataCode = segment.departure.iataCode;
      return formatAirportDisplay(iataCode, "city");
    });
  };

  // Format duration
  const formatDuration = (duration) => {
    if (!duration) return "N/A";
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
    const hours = match[1] ? `${match[1]}h ` : "";
    const minutes = match[2] ? `${match[2]}m` : "";
    return hours + minutes || "N/A";
  };

  // Get flight segment
  const getFlightSegment = (flight, index = 0) => {
    try {
      return flight.itineraries[0].segments[index] || {};
    } catch (error) {
      console.error("Error accessing flight segment:", error);
      return {};
    }
  };

  const transitCount = getTransitCount(flight);
  const transitInfo = getTransitInfo(flight);
  const firstSegment = getFlightSegment(flight, 0);
  const lastSegment = getFlightSegment(
    flight,
    flight.itineraries?.[0]?.segments?.length - 1
  );
  const carrierCode = firstSegment.carrierCode || "DEFAULT";
  const airline = getAirlineByCode(carrierCode);

  return (
    <div
      className={`row flight-item p-3 border mt-2 align-items-center ${
        isSelected ? "selected-flight" : ""
      }`}
    >
      <div className="col-md-2 d-flex flex-column align-items-center">
        <div
          className="airline-logo-container"
          style={{
            width: "100px",
            height: "100px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <img
            src={airline.logo}
            alt={airline.name}
            style={{
              maxWidth: "150%",
              maxHeight: "100%",
              objectFit: "contain",
            }}
            className="me-2"
            onError={(e) => {
              // First fallback: Try avs.io service with the airline code
              console.log(`Trying fallback for airline: ${carrierCode}`);
              e.target.src = `https://pics.avs.io/200/200/${carrierCode}.png`;

              // If that fails, use the default airplane icon
              e.target.onerror = () => {
                console.log(`Using default icon for airline: ${carrierCode}`);
                e.target.src =
                  "https://cdn-icons-png.flaticon.com/512/187/187820.png";
                e.target.onerror = null; // Prevent infinite loop
              };
            }}
          />
        </div>
        <small className="text-muted text-center fw-bold p-1 mt-1">
          {airline.name || carrierCode}
        </small>
      </div>

      <div className="col-md-2 text-center">
        <div className="fw-bold">{carrierCode}</div>
        <small className="">{firstSegment.number || ""}</small>
      </div>

      <div className="col-md-2 text-center">
        <div className="fw-bold">
          {firstSegment.departure?.at
            ? new Date(firstSegment.departure.at).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            : "N/A"}
        </div>
        <div className="small text-muted">
          {formatAirportDisplay(firstSegment.departure?.iataCode, "city")}
        </div>
        <div className="small text-muted">
          {formatAirportDisplay(firstSegment.departure?.iataCode, "country")}
        </div>
      </div>

      <div className="col-md-2 text-center">
        <div className="fw-bold">
          {formatDuration(flight.itineraries?.[0]?.duration)}
        </div>
        <small>
          {transitCount === 0 ? (
            <span className="text-success m-2">Direct</span>
          ) : (
            <span>
              {transitCount} {transitCount === 1 ? "Stop" : "Stops"}
            </span>
          )}
        </small>
        <small>
          {transitCount > 0 && (
            <div className="small fw-bold p-2">
              Via: {transitInfo.join(", ")}
            </div>
          )}
        </small>
      </div>

      <div className="col-md-2 text-center">
        <div className="fw-bold">
          {lastSegment.arrival?.at
            ? new Date(lastSegment.arrival.at).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            : "N/A"}
        </div>
        <div className="small text-muted">
          {formatAirportDisplay(lastSegment.arrival?.iataCode, "city")}
        </div>
        <div className="small text-muted">
          {formatAirportDisplay(lastSegment.arrival?.iataCode, "country")}
        </div>
      </div>

      <div className="col-md-2 text-end text-center">
        <div className="fw-bold text-success fs-5">
          {flight.price?.currency || ""} {flight.price?.total || "N/A"}
        </div>
        <button
          onClick={() => onSelect(flight)}
          className={`btn btn-sm ${isSelected ? "btn-success" : "btn-primary"}`}
          disabled={isSelected}
        >
          {isSelected ? selectedButtonText : buttonText}
        </button>
      </div>
    </div>
  );
};

export default FlightItem;
