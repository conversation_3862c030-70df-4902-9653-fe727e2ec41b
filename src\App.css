/* Global loading indicator */
.global-loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.global-loading-indicator .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Theme support */
body[data-theme="dark"] {
  background-color: #121212;
  color: #e0e0e0;
}

body[data-theme="dark"] .card {
  background-color: #1e1e1e;
  border-color: #333;
}

body[data-theme="dark"] .card-header {
  background-color: #2c2c2c;
  border-color: #333;
}

body[data-theme="dark"] .form-control {
  background-color: #2c2c2c;
  border-color: #444;
  color: #e0e0e0;
}

body[data-theme="dark"] .form-control:focus {
  background-color: #2c2c2c;
  color: #e0e0e0;
}

body[data-theme="dark"] .btn-light {
  background-color: #2c2c2c;
  border-color: #444;
  color: #e0e0e0;
}

body[data-theme="dark"] .modal-content {
  background-color: #1e1e1e;
  color: #e0e0e0;
}

/* Error boundary styles */
.error-boundary {
  margin: 2rem auto;
  max-width: 800px;
}

.error-details summary {
  cursor: pointer;
  padding: 0.5rem 0;
}

.error-details pre {
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}