.payment-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

.payment-container h3 {
  color: var(--primary);
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 0.75rem;
}

.payment-summary {
  background-color: white;
  border-radius: var(--border-radius-md);
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: var(--shadow-sm);
  border: 1px solid #e0e0e0;
}

.payment-summary h4 {
  margin-bottom: 15px;
  color: var(--dark);
  font-weight: 600;
}

.payment-methods {
  margin-bottom: 30px;
}

.payment-methods h4 {
  margin-bottom: 20px;
  color: var(--dark);
  font-weight: 600;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: var(--transition-base);
}

.payment-option:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.payment-option.active {
  border-color: var(--primary);
  background-color: var(--primary-light);
}

.payment-option-icon {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--primary);
  background-color: var(--light);
  border-radius: var(--border-radius-circle);
}

.payment-option-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.payment-option-details {
  flex: 1;
}

.payment-option-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--dark);
}

.payment-option-description {
  font-size: 0.85rem;
  color: var(--secondary);
}

/* Card form styling */
.card-form {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

/* Mobile banking section styling */
.mobile-banking-section {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
  margin-bottom: 20px;
}

.mobile-banking-logo {
  height: 60px;
  margin-bottom: 15px;
  object-fit: contain;
}

.mobile-banking-instructions {
  background-color: var(--light);
  padding: 15px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.mobile-banking-instructions ol {
  margin-bottom: 0;
  padding-left: 20px;
}

/* Bank transfer section styling */
.bank-transfer-section {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.bank-details {
  background-color: var(--light);
  padding: 15px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
}

.bank-details p {
  margin-bottom: 8px;
}

.bank-details strong {
  color: var(--dark);
}

/* PayPal section styling */
.paypal-section {
  text-align: center;
  padding: 30px;
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid #dee2e6;
}

.paypal-logo {
  max-width: 120px;
  margin-bottom: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .payment-container {
    padding: 15px;
  }

  .payment-option {
    padding: 12px;
  }

  .payment-option-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}
