.payment-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  );
  border-radius: var(--border-radius-md);
  box-shadow: 0 10px 30px rgba(2, 48, 71, 0.1);
  min-height: 100vh;
}

.payment-container h3 {
  color: var(--primary-dark);
  margin-bottom: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--accent-yellow);
  padding-bottom: 0.75rem;
  text-align: center;
}

.payment-summary {
  background-color: white;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 10px 30px rgba(2, 48, 71, 0.1);
  border: none;
  border-top: 4px solid var(--accent-yellow);
}

.payment-summary h4 {
  margin-bottom: 15px;
  color: var(--primary-dark);
  font-weight: 600;
}

.payment-methods {
  margin-bottom: 30px;
}

.payment-methods h4 {
  margin-bottom: 20px;
  color: var(--primary-dark);
  font-weight: 600;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 15px;
  background-color: white;
  border: 2px solid rgba(142, 202, 230, 0.3);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(2, 48, 71, 0.1);
}

.payment-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(2, 48, 71, 0.15);
  border-color: var(--primary-medium);
}

.payment-option.active {
  border-color: var(--accent-yellow);
  background: linear-gradient(
    135deg,
    rgba(255, 183, 3, 0.1),
    rgba(251, 133, 0, 0.1)
  );
  box-shadow: 0 8px 25px rgba(255, 183, 3, 0.2);
}

.payment-option-icon {
  width: 60px;
  height: 60px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: var(--primary-medium);
  background: linear-gradient(
    135deg,
    var(--primary-light),
    rgba(142, 202, 230, 0.3)
  );
  border-radius: var(--border-radius-circle);
  box-shadow: 0 4px 10px rgba(2, 48, 71, 0.1);
}

.payment-option-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.payment-option-details {
  flex: 1;
}

.payment-option-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--primary-dark);
}

.payment-option-description {
  font-size: 0.85rem;
  color: var(--medium-gray);
}

/* Card form styling */
.card-form {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

/* Mobile banking section styling */
.mobile-banking-section {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
  margin-bottom: 20px;
}

.mobile-banking-logo {
  height: 60px;
  margin-bottom: 15px;
  object-fit: contain;
}

.mobile-banking-instructions {
  background-color: var(--light);
  padding: 15px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.mobile-banking-instructions ol {
  margin-bottom: 0;
  padding-left: 20px;
}

/* Bank transfer section styling */
.bank-transfer-section {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-md);
  padding: 25px;
  background-color: white;
  box-shadow: var(--shadow-sm);
}

.bank-details {
  background-color: var(--light);
  padding: 15px;
  border-radius: var(--border-radius-md);
  margin-bottom: 20px;
}

.bank-details p {
  margin-bottom: 8px;
}

.bank-details strong {
  color: var(--dark);
}

/* PayPal section styling */
.paypal-section {
  text-align: center;
  padding: 30px;
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid #dee2e6;
}

.paypal-logo {
  max-width: 120px;
  margin-bottom: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .payment-container {
    padding: 15px;
  }

  .payment-option {
    padding: 12px;
  }

  .payment-option-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}
