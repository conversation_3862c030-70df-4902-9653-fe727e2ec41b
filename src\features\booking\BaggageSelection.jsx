import React, { useState, useEffect } from "react";
import "../../assets/styles/Booking.css";
import "../../assets/styles/BaggageSelection.css";

/**
 * Component for baggage selection during the booking process
 */
const BaggageSelection = ({ 
  selectedFlights, 
  passengers, 
  pricingOption, 
  onComplete, 
  onSkip 
}) => {
  // State for baggage selections
  const [baggageSelections, setBaggageSelections] = useState({
    outbound: {},
    return: selectedFlights.return ? {} : null,
  });

  // State for validation errors
  const [errors, setErrors] = useState([]);

  // Baggage options with prices
  const baggageOptions = {
    none: { weight: 0, price: 0, label: "No Extra Baggage" },
    carry: { weight: 7, price: 0, label: "Carry-on Only (7kg)" },
    checked15: { weight: 15, price: 1500, label: "15kg Checked Baggage" },
    checked20: { weight: 20, price: 2000, label: "20kg Checked Baggage" },
    checked25: { weight: 25, price: 2500, label: "25kg Checked Baggage" },
    checked30: { weight: 30, price: 3000, label: "30kg Checked Baggage" },
    excess: { weight: 40, price: 4000, label: "Excess Baggage (40kg)" },
  };

  // Initialize baggage selections for all passengers
  useEffect(() => {
    const initialSelections = {
      outbound: {},
      return: selectedFlights.return ? {} : null,
    };

    passengers.forEach((passenger) => {
      initialSelections.outbound[passenger.id] = "carry"; // Default to carry-on
      if (selectedFlights.return) {
        initialSelections.return[passenger.id] = "carry";
      }
    });

    setBaggageSelections(initialSelections);
  }, [passengers, selectedFlights.return]);

  // Handle baggage selection change
  const handleBaggageChange = (flightType, passengerId, baggageType) => {
    setBaggageSelections(prev => ({
      ...prev,
      [flightType]: {
        ...prev[flightType],
        [passengerId]: baggageType,
      },
    }));

    // Clear any errors when selection changes
    setErrors([]);
  };

  // Calculate total baggage cost
  const calculateTotalBaggageCost = () => {
    let total = 0;

    // Calculate outbound baggage cost
    Object.values(baggageSelections.outbound || {}).forEach((baggageType) => {
      total += baggageOptions[baggageType]?.price || 0;
    });

    // Calculate return baggage cost
    if (baggageSelections.return) {
      Object.values(baggageSelections.return).forEach((baggageType) => {
        total += baggageOptions[baggageType]?.price || 0;
      });
    }

    return total;
  };

  // Validate baggage selections
  const validateBaggage = () => {
    const newErrors = [];

    // Check if all passengers have baggage selected
    passengers.forEach((passenger) => {
      if (!baggageSelections.outbound[passenger.id]) {
        newErrors.push(`Please select baggage for ${passenger.firstName} ${passenger.lastName} on outbound flight`);
      }
      
      if (selectedFlights.return && !baggageSelections.return[passenger.id]) {
        newErrors.push(`Please select baggage for ${passenger.firstName} ${passenger.lastName} on return flight`);
      }
    });

    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateBaggage()) {
      const baggageData = {
        selections: baggageSelections,
        totalCost: calculateTotalBaggageCost(),
        details: {
          outbound: Object.entries(baggageSelections.outbound || {}).map(([passengerId, baggageType]) => ({
            passengerId,
            passenger: passengers.find(p => p.id === passengerId)?.firstName || "",
            baggageType,
            weight: baggageOptions[baggageType]?.weight || 0,
            price: baggageOptions[baggageType]?.price || 0,
          })),
          return: selectedFlights.return 
            ? Object.entries(baggageSelections.return || {}).map(([passengerId, baggageType]) => ({
                passengerId,
                passenger: passengers.find(p => p.id === passengerId)?.firstName || "",
                baggageType,
                weight: baggageOptions[baggageType]?.weight || 0,
                price: baggageOptions[baggageType]?.price || 0,
              }))
            : [],
        },
      };

      onComplete(baggageData);
    }
  };

  // Handle skipping baggage selection
  const handleSkipBaggage = () => {
    if (onSkip) {
      onSkip();
    }
  };

  // Render baggage option card
  const renderBaggageOption = (baggageType, option, flightType, passengerId) => {
    const isSelected = baggageSelections[flightType]?.[passengerId] === baggageType;
    const isRecommended = baggageType === 'checked20';

    return (
      <div
        key={baggageType}
        className={`baggage-option ${isSelected ? 'selected' : ''} ${isRecommended ? 'recommended' : ''}`}
        onClick={() => handleBaggageChange(flightType, passengerId, baggageType)}
      >
        {isRecommended && (
          <div className="recommended-badge">
            <i className="fas fa-star"></i> Recommended
          </div>
        )}
        <div className="baggage-icon">
          <i className={`fas ${option.weight === 0 ? 'fa-ban' : option.weight <= 7 ? 'fa-briefcase' : 'fa-suitcase'}`}></i>
        </div>
        <div className="baggage-details">
          <h6 className="baggage-label">{option.label}</h6>
          <div className="baggage-weight">{option.weight}kg</div>
          <div className="baggage-price">
            {option.price === 0 ? 'Free' : `৳${option.price}`}
          </div>
        </div>
        {isSelected && (
          <div className="selected-indicator">
            <i className="fas fa-check-circle"></i>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="baggage-selection-container">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h3>Select Your Baggage (Optional)</h3>
          <p className="text-muted mb-0">
            Choose baggage allowance for each passenger and flight. Additional charges apply for checked baggage.
          </p>
        </div>
        <button
          type="button"
          className="btn btn-outline-secondary"
          onClick={handleSkipBaggage}
        >
          <i className="fas fa-forward me-2"></i>
          Skip Baggage Selection
        </button>
      </div>

      {errors.length > 0 && (
        <div className="alert alert-danger">
          <ul className="mb-0">
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        {passengers.map((passenger) => (
          <div key={passenger.id} className="passenger-baggage-section mb-5">
            <div className="passenger-header mb-3">
              <h5>
                {passenger.firstName} {passenger.lastName}
                <span className={`badge ms-2 ${
                  passenger.type === "ADULT" ? "bg-primary" :
                  passenger.type === "CHILD" ? "bg-info" :
                  "bg-warning text-dark"
                }`}>
                  {passenger.type}
                </span>
              </h5>
            </div>

            {/* Outbound Flight Baggage */}
            <div className="flight-baggage-section mb-4">
              <h6 className="flight-header">
                <i className="fas fa-plane-departure me-2 text-primary"></i>
                Outbound Flight Baggage
              </h6>
              <div className="baggage-options-grid">
                {Object.entries(baggageOptions).map(([baggageType, option]) =>
                  renderBaggageOption(baggageType, option, 'outbound', passenger.id)
                )}
              </div>
            </div>

            {/* Return Flight Baggage */}
            {selectedFlights.return && (
              <div className="flight-baggage-section mb-4">
                <h6 className="flight-header">
                  <i className="fas fa-plane-arrival me-2 text-secondary"></i>
                  Return Flight Baggage
                </h6>
                <div className="baggage-options-grid">
                  {Object.entries(baggageOptions).map(([baggageType, option]) =>
                    renderBaggageOption(baggageType, option, 'return', passenger.id)
                  )}
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Baggage Information */}
        <div className="card mb-4">
          <div className="card-header">
            <h6 className="mb-0">
              <i className="fas fa-info-circle me-2"></i>
              Baggage Information
            </h6>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6">
                <h6>Carry-on Baggage (Free)</h6>
                <ul className="list-unstyled small text-muted">
                  <li>• Maximum 7kg weight</li>
                  <li>• Dimensions: 55cm x 40cm x 20cm</li>
                  <li>• Must fit in overhead compartment</li>
                </ul>
              </div>
              <div className="col-md-6">
                <h6>Checked Baggage</h6>
                <ul className="list-unstyled small text-muted">
                  <li>• Additional charges apply</li>
                  <li>• Maximum dimensions: 158cm total</li>
                  <li>• Excess weight charges: ৳200/kg</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Total Cost */}
        <div className="card mb-4">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Total Baggage Cost:</h5>
              <h5 className="mb-0 text-primary">৳{calculateTotalBaggageCost()}</h5>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="row gap-2">
          <div className="col-md-6">
            <button type="submit" className="btn btn-primary btn-lg w-100">
              <i className="fas fa-chair me-2"></i>
              Continue to Seat Selection
            </button>
          </div>
          <div className="col-md-6">
            <button
              type="button"
              className="btn btn-outline-secondary btn-lg w-100"
              onClick={handleSkipBaggage}
            >
              <i className="fas fa-forward me-2"></i>
              Continue without Extra Baggage
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default BaggageSelection;
