import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { AppProvider, useAppContext } from "../../context/AppContext";
import HotelSearch from "../HotelSearch";
import {
  searchHotelLocations,
  searchHotelsByCity,
  searchHotelOffers,
} from "../../services/hotelService";

// Mock the hotel service
jest.mock("../../services/hotelService", () => ({
  searchHotelLocations: jest.fn(),
  searchHotelsByCity: jest.fn(),
  searchHotelOffers: jest.fn(),
}));

// Mock react-datepicker
jest.mock("react-datepicker", () => {
  return function MockDatePicker({ selected, onChange, ...props }) {
    return (
      <input
        type="date"
        value={selected ? selected.toISOString().split("T")[0] : ""}
        onChange={(e) => onChange(new Date(e.target.value))}
        {...props}
      />
    );
  };
});

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <AppProvider>{children}</AppProvider>
  </BrowserRouter>
);

describe("Hotel Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("hotel search component renders correctly", () => {
    render(
      <TestWrapper>
        <HotelSearch onSearchResults={jest.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText("Search Hotels")).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter city or hotel name")
    ).toBeInTheDocument();
    expect(screen.getByText("Check-in")).toBeInTheDocument();
    expect(screen.getByText("Check-out")).toBeInTheDocument();
    expect(screen.getByText("Adults")).toBeInTheDocument();
    expect(screen.getByText("Rooms")).toBeInTheDocument();
  });

  test("destination autocomplete works", async () => {
    const mockLocations = [
      {
        name: "Dhaka",
        detailedName: "Dhaka, Bangladesh",
        iataCode: "DAC",
      },
      {
        name: "Cox's Bazar",
        detailedName: "Cox's Bazar, Bangladesh",
        iataCode: "CXB",
      },
    ];

    searchHotelLocations.mockResolvedValue(mockLocations);

    render(
      <TestWrapper>
        <HotelSearch onSearchResults={jest.fn()} />
      </TestWrapper>
    );

    const destinationInput = screen.getByPlaceholderText(
      "Enter city or hotel name"
    );

    fireEvent.change(destinationInput, { target: { value: "Dha" } });

    await waitFor(() => {
      expect(searchHotelLocations).toHaveBeenCalledWith("Dha");
    });

    await waitFor(() => {
      expect(screen.getByText("Dhaka")).toBeInTheDocument();
    });
  });

  test("hotel search validation works", async () => {
    const mockOnSearchResults = jest.fn();

    render(
      <TestWrapper>
        <HotelSearch onSearchResults={mockOnSearchResults} />
      </TestWrapper>
    );

    const searchButton = screen.getByText("Search Hotels");

    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(
        screen.getByText("Please enter a destination")
      ).toBeInTheDocument();
    });

    expect(mockOnSearchResults).not.toHaveBeenCalled();
  });

  test("successful hotel search flow", async () => {
    const mockHotels = [
      {
        hotelId: "DHCOXBAZ",
      },
    ];

    const mockHotelOffers = [
      {
        type: "hotel-offers",
        hotel: {
          hotelId: "DHCOXBAZ",
          name: "Cox's Bazar Beach Resort",
          rating: "4",
          address: {
            cityName: "Cox's Bazar",
            countryCode: "BD",
          },
        },
        offers: [
          {
            id: "OFFER123",
            price: {
              total: "8500.00",
              currency: "BDT",
            },
            room: {
              description: {
                text: "Superior King Room",
              },
            },
          },
        ],
      },
    ];

    searchHotelsByCity.mockResolvedValue(mockHotels);
    searchHotelOffers.mockResolvedValue(mockHotelOffers);

    const mockOnSearchResults = jest.fn();

    render(
      <TestWrapper>
        <HotelSearch onSearchResults={mockOnSearchResults} />
      </TestWrapper>
    );

    // Fill in the form
    const destinationInput = screen.getByPlaceholderText(
      "Enter city or hotel name"
    );
    fireEvent.change(destinationInput, { target: { value: "Cox's Bazar" } });

    const checkInInput = screen.getByDisplayValue("");
    fireEvent.change(checkInInput, { target: { value: "2024-12-25" } });

    const checkOutInput = screen.getAllByDisplayValue("")[1];
    fireEvent.change(checkOutInput, { target: { value: "2024-12-27" } });

    const searchButton = screen.getByText("Search Hotels");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(searchHotelsByCity).toHaveBeenCalledWith({ cityCode: "DAC" });
    });

    await waitFor(() => {
      expect(searchHotelOffers).toHaveBeenCalledWith(
        expect.objectContaining({
          hotelIds: "DHCOXBAZ",
          checkInDate: "2024-12-25",
          checkOutDate: "2024-12-27",
          adults: 1,
          roomQuantity: 1,
          currency: "BDT",
        })
      );
    });

    await waitFor(() => {
      expect(mockOnSearchResults).toHaveBeenCalledWith(
        mockHotelOffers,
        expect.objectContaining({
          hotelIds: "DHCOXBAZ",
          checkInDate: "2024-12-25",
          checkOutDate: "2024-12-27",
        })
      );
    });
  });

  test("handles search errors gracefully", async () => {
    searchHotelsByCity.mockRejectedValue(new Error("API Error"));

    render(
      <TestWrapper>
        <HotelSearch onSearchResults={jest.fn()} />
      </TestWrapper>
    );

    // Fill in the form
    const destinationInput = screen.getByPlaceholderText(
      "Enter city or hotel name"
    );
    fireEvent.change(destinationInput, { target: { value: "Dhaka" } });

    const searchButton = screen.getByText("Search Hotels");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(
        screen.getByText("Hotel search failed. Please try again.")
      ).toBeInTheDocument();
    });
  });

  test("date validation works correctly", async () => {
    render(
      <TestWrapper>
        <HotelSearch onSearchResults={jest.fn()} />
      </TestWrapper>
    );

    const destinationInput = screen.getByPlaceholderText(
      "Enter city or hotel name"
    );
    fireEvent.change(destinationInput, { target: { value: "Dhaka" } });

    // Set check-out date before check-in date
    const checkInInput = screen.getByDisplayValue("");
    fireEvent.change(checkInInput, { target: { value: "2024-12-27" } });

    const checkOutInput = screen.getAllByDisplayValue("")[1];
    fireEvent.change(checkOutInput, { target: { value: "2024-12-25" } });

    const searchButton = screen.getByText("Search Hotels");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(
        screen.getByText("Check-out date must be after check-in date")
      ).toBeInTheDocument();
    });
  });

  test("guest count validation works", async () => {
    render(
      <TestWrapper>
        <HotelSearch onSearchResults={jest.fn()} />
      </TestWrapper>
    );

    const destinationInput = screen.getByPlaceholderText(
      "Enter city or hotel name"
    );
    fireEvent.change(destinationInput, { target: { value: "Dhaka" } });

    // Set adults to 0
    const adultsSelect = screen.getByDisplayValue("1 Adult");
    fireEvent.change(adultsSelect, { target: { value: "0" } });

    const searchButton = screen.getByText("Search Hotels");
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(
        screen.getByText("At least 1 adult is required")
      ).toBeInTheDocument();
    });
  });
});

// Integration test for the complete hotel booking flow
describe("Hotel Booking Flow Integration", () => {
  test("hotel booking data flows correctly through context", () => {
    const TestComponent = () => {
      const { state, dispatch } = useAppContext();

      const handleSetBookingData = () => {
        dispatch({
          type: "SAVE_FORM_DATA",
          payload: {
            hotelBookingData: {
              hotelOffer: { hotel: { name: "Test Hotel" } },
              selectedRooms: [{ price: { total: "5000" } }],
              searchParams: { checkInDate: "2024-12-25" },
            },
          },
        });
      };

      return (
        <div>
          <button onClick={handleSetBookingData}>Set Booking Data</button>
          <div data-testid="booking-data">
            {state.formData?.hotelBookingData?.hotelOffer?.hotel?.name ||
              "No data"}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId("booking-data")).toHaveTextContent("No data");

    fireEvent.click(screen.getByText("Set Booking Data"));

    expect(screen.getByTestId("booking-data")).toHaveTextContent("Test Hotel");
  });
});
