import { request } from "./apiClient";

/**
 * Search for hotels by city using Amadeus Hotel Search API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers
 */
export const searchHotelsByCity = async (params) => {
  try {
    const response = await request({
      url: "/v1/reference-data/locations/hotels/by-city",
      method: "get",
      params: {
        cityCode: params.cityCode,
        radius: params.radius || 5,
        radiusUnit: params.radiusUnit || "KM",
        hotelSource: params.hotelSource || "ALL",
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel search by city failed:", error);
    throw error;
  }
};

/**
 * Search for hotels by geocode using Amadeus Hotel Search API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers
 */
export const searchHotelsByGeocode = async (params) => {
  try {
    const response = await request({
      url: "/v1/reference-data/locations/hotels/by-geocode",
      method: "get",
      params: {
        latitude: params.latitude,
        longitude: params.longitude,
        radius: params.radius || 5,
        radiusUnit: params.radiusUnit || "KM",
        hotelSource: params.hotelSource || "ALL",
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel search by geocode failed:", error);
    throw error;
  }
};

/**
 * Search for hotel offers using Amadeus Hotel Shopping API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers with pricing
 */
export const searchHotelOffers = async (params) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock hotel offers");
      return generateMockHotelOffers(params);
    }

    const response = await request({
      url: "/v3/shopping/hotel-offers",
      method: "get",
      params: {
        hotelIds: params.hotelIds,
        adults: params.adults || 1,
        checkInDate: params.checkInDate,
        checkOutDate: params.checkOutDate,
        roomQuantity: params.roomQuantity || 1,
        currency: params.currency || "BDT",
        lang: params.lang || "EN",
        includeClosed: params.includeClosed || false,
        bestRateOnly: params.bestRateOnly || true,
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel offers search failed:", error);
    throw error;
  }
};

/**
 * Get hotel details by hotel ID
 * @param {string} hotelId - Hotel ID
 * @returns {Promise<Object>} Hotel details
 */
export const getHotelDetails = async (hotelId) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock hotel details");
      return generateMockHotelDetails(hotelId);
    }

    const response = await request({
      url: `/v1/reference-data/locations/hotels/${hotelId}`,
      method: "get",
    });
    return response.data?.data || {};
  } catch (error) {
    console.error("Hotel details fetch failed:", error);
    throw error;
  }
};

/**
 * Create a hotel booking using Amadeus Hotel Booking API
 * @param {Object} hotelOffer - Selected hotel offer
 * @param {Array} guests - Guest details
 * @param {Object} payments - Payment information
 * @returns {Promise<Object>} Booking confirmation
 */
export const createHotelBooking = async (hotelOffer, guests, payments) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - creating mock hotel booking");
      const mockConfirmationNumber = `HTL-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      return {
        data: {
          type: "hotel-booking",
          id: `DEV-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
          providerConfirmationId: mockConfirmationNumber,
          associatedRecords: [
            {
              reference: mockConfirmationNumber,
              originSystemCode: "AMADEUS",
            },
          ],
          guests: guests.map((guest, index) => ({
            ...guest,
            id: `GT${index + 1}`,
          })),
          hotelOffer,
        },
        confirmationNumber: mockConfirmationNumber,
        status: "CONFIRMED",
      };
    }

    // Production mode
    const response = await request({
      url: "/v1/booking/hotel-bookings",
      method: "post",
      data: {
        data: {
          type: "hotel-booking",
          hotelOffer,
          guests,
          payments,
        },
      },
    });

    const bookingData = response.data.data;
    const confirmationNumber =
      bookingData.providerConfirmationId ||
      bookingData.associatedRecords?.[0]?.reference ||
      `HTL-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;

    return {
      ...response.data,
      confirmationNumber,
      status: bookingData.bookingStatus || "CONFIRMED",
    };
  } catch (error) {
    console.error("Hotel booking creation failed:", error);
    throw error;
  }
};

/**
 * Format guest data for hotel booking API request
 * @param {Array} guests - Guest details from form
 * @returns {Array} Formatted guests for API
 */
export const formatHotelGuests = (guests) => {
  return guests.map((guest, index) => ({
    id: `GT${index + 1}`,
    name: {
      title: guest.title || "MR",
      firstName: guest.firstName,
      lastName: guest.lastName,
    },
    contact: {
      phone: guest.phone || "+8801234567890",
      email: guest.email || "<EMAIL>",
    },
    dateOfBirth: guest.dateOfBirth,
    ...(guest.specialRequests && { specialRequests: guest.specialRequests }),
  }));
};

/**
 * Search for cities/locations for hotel search
 * @param {string} query - Search query
 * @returns {Promise<Array>} Location suggestions
 */
export const searchHotelLocations = async (query) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock locations");
      return generateMockLocations(query);
    }

    const response = await request({
      url: "/v1/reference-data/locations",
      method: "get",
      params: {
        keyword: query,
        subType: "CITY,AIRPORT",
        countryCode: "BD", // Focus on Bangladesh
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Location search failed:", error);
    throw error;
  }
};

/**
 * Get hotel booking by confirmation number
 * @param {string} confirmationNumber - Hotel booking confirmation number
 * @returns {Promise<Object>} Hotel booking details
 */
export const getHotelBooking = async (confirmationNumber) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock booking details");
      return generateMockBookingDetails(confirmationNumber);
    }

    const response = await request({
      url: `/v1/booking/hotel-bookings/${encodeURIComponent(confirmationNumber)}`,
      method: "get",
    });

    return {
      ...response.data,
      confirmationNumber,
    };
  } catch (error) {
    console.error("Hotel booking retrieval failed:", error);
    throw error;
  }
};

/**
 * Cancel hotel booking
 * @param {string} confirmationNumber - Hotel booking confirmation number
 * @returns {Promise<Object>} Cancellation confirmation
 */
export const cancelHotelBooking = async (confirmationNumber) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - simulating booking cancellation");
      return {
        status: "CANCELLED",
        confirmationNumber,
        cancellationDate: new Date().toISOString(),
        refundAmount: "8500.00",
        currency: "BDT",
      };
    }

    const response = await request({
      url: `/v1/booking/hotel-bookings/${encodeURIComponent(confirmationNumber)}`,
      method: "delete",
    });

    return response.data;
  } catch (error) {
    console.error("Hotel booking cancellation failed:", error);
    throw error;
  }
};

// ==================== MOCK DATA GENERATORS ====================

/**
 * Generate mock hotel offers for development
 * @param {Object} params - Search parameters
 * @returns {Array} Mock hotel offers
 */
const generateMockHotelOffers = (params) => {
  const mockHotels = [
    {
      type: "hotel-offers",
      hotel: {
        type: "hotel",
        hotelId: "DHCOXBAZ",
        chainCode: "RT",
        dupeId: "700001234",
        name: "Cox's Bazar Beach Resort",
        rating: "4",
        cityCode: "CXB",
        latitude: 21.4272,
        longitude: 92.0058,
        hotelDistance: {
          distance: 0.5,
          distanceUnit: "KM",
        },
        address: {
          lines: ["Beach Road"],
          postalCode: "4700",
          cityName: "Cox's Bazar",
          countryCode: "BD",
        },
        contact: {
          phone: "+880-341-64001",
          fax: "+880-341-64002",
          email: "<EMAIL>",
        },
        amenities: [
          "SWIMMING_POOL",
          "RESTAURANT",
          "WIFI",
          "SPA",
          "FITNESS_CENTER",
        ],
      },
      available: true,
      offers: [
        {
          id: "OFFER1234567890",
          checkInDate: params.checkInDate,
          checkOutDate: params.checkOutDate,
          rateCode: "RAC",
          rateFamilyEstimated: {
            code: "SRS",
            type: "P",
          },
          room: {
            type: "A2K",
            typeEstimated: {
              category: "SUPERIOR_ROOM",
              beds: 1,
              bedType: "KING",
            },
            description: {
              text: "Superior King Room with Sea View",
            },
          },
          guests: {
            adults: params.adults || 1,
          },
          price: {
            currency: "BDT",
            base: "8500.00",
            total: "9350.00",
            taxes: [
              {
                code: "VAT",
                amount: "850.00",
                currency: "BDT",
                included: false,
              },
            ],
          },
          policies: {
            paymentType: "guarantee",
            cancellation: {
              deadline: params.checkInDate,
              amount: "8500.00",
              type: "FULL_STAY",
            },
          },
          self: "https://test.api.amadeus.com/v3/shopping/hotel-offers/OFFER1234567890",
        },
      ],
      self: "https://test.api.amadeus.com/v3/shopping/hotel-offers?hotelIds=DHCOXBAZ",
    },
    {
      type: "hotel-offers",
      hotel: {
        type: "hotel",
        hotelId: "DHDHAKA",
        chainCode: "HI",
        dupeId: "*********",
        name: "Dhaka Regency Hotel",
        rating: "5",
        cityCode: "DAC",
        latitude: 23.8103,
        longitude: 90.4125,
        hotelDistance: {
          distance: 2.1,
          distanceUnit: "KM",
        },
        address: {
          lines: ["Gulshan Avenue"],
          postalCode: "1212",
          cityName: "Dhaka",
          countryCode: "BD",
        },
        contact: {
          phone: "+880-2-9881234",
          fax: "+880-2-9881235",
          email: "<EMAIL>",
        },
        amenities: [
          "SWIMMING_POOL",
          "RESTAURANT",
          "WIFI",
          "SPA",
          "FITNESS_CENTER",
          "BUSINESS_CENTER",
          "CONCIERGE",
        ],
      },
      available: true,
      offers: [
        {
          id: "OFFER1234567891",
          checkInDate: params.checkInDate,
          checkOutDate: params.checkOutDate,
          rateCode: "RAC",
          rateFamilyEstimated: {
            code: "DLX",
            type: "P",
          },
          room: {
            type: "A1Q",
            typeEstimated: {
              category: "DELUXE_ROOM",
              beds: 1,
              bedType: "QUEEN",
            },
            description: {
              text: "Deluxe Queen Room with City View",
            },
          },
          guests: {
            adults: params.adults || 1,
          },
          price: {
            currency: "BDT",
            base: "12000.00",
            total: "13200.00",
            taxes: [
              {
                code: "VAT",
                amount: "1200.00",
                currency: "BDT",
                included: false,
              },
            ],
          },
          policies: {
            paymentType: "guarantee",
            cancellation: {
              deadline: params.checkInDate,
              amount: "12000.00",
              type: "FULL_STAY",
            },
          },
          self: "https://test.api.amadeus.com/v3/shopping/hotel-offers/OFFER1234567891",
        },
      ],
      self: "https://test.api.amadeus.com/v3/shopping/hotel-offers?hotelIds=DHDHAKA",
    },
  ];

  return mockHotels;
};

/**
 * Generate mock hotel details for development
 * @param {string} hotelId - Hotel ID
 * @returns {Object} Mock hotel details
 */
const generateMockHotelDetails = (hotelId) => {
  const hotelDetails = {
    DHCOXBAZ: {
      type: "location",
      subType: "HOTEL",
      id: hotelId,
      name: "Cox's Bazar Beach Resort",
      iataCode: "CXB",
      address: {
        cityName: "Cox's Bazar",
        countryCode: "BD",
      },
      geoCode: {
        latitude: 21.4272,
        longitude: 92.0058,
      },
      distance: {
        value: 0.5,
        unit: "KM",
      },
      lastUpdate: new Date().toISOString(),
    },
    DHDHAKA: {
      type: "location",
      subType: "HOTEL",
      id: hotelId,
      name: "Dhaka Regency Hotel",
      iataCode: "DAC",
      address: {
        cityName: "Dhaka",
        countryCode: "BD",
      },
      geoCode: {
        latitude: 23.8103,
        longitude: 90.4125,
      },
      distance: {
        value: 2.1,
        unit: "KM",
      },
      lastUpdate: new Date().toISOString(),
    },
  };

  return hotelDetails[hotelId] || hotelDetails.DHCOXBAZ;
};

/**
 * Generate mock locations for development
 * @param {string} query - Search query
 * @returns {Array} Mock location suggestions
 */
const generateMockLocations = (query) => {
  const mockLocations = [
    {
      type: "location",
      subType: "CITY",
      name: "Dhaka",
      detailedName: "Dhaka, Bangladesh",
      id: "CDHAKA",
      self: {
        href: "https://test.api.amadeus.com/v1/reference-data/locations/CDHAKA",
        methods: ["GET"],
      },
      timeZoneOffset: "+06:00",
      iataCode: "DAC",
      geoCode: {
        latitude: 23.8103,
        longitude: 90.4125,
      },
      address: {
        cityName: "Dhaka",
        countryName: "Bangladesh",
        countryCode: "BD",
        regionCode: "ASIA",
      },
      analytics: {
        travelers: {
          score: 27,
        },
      },
    },
    {
      type: "location",
      subType: "CITY",
      name: "Cox's Bazar",
      detailedName: "Cox's Bazar, Bangladesh",
      id: "CCOXBAZ",
      self: {
        href: "https://test.api.amadeus.com/v1/reference-data/locations/CCOXBAZ",
        methods: ["GET"],
      },
      timeZoneOffset: "+06:00",
      iataCode: "CXB",
      geoCode: {
        latitude: 21.4272,
        longitude: 92.0058,
      },
      address: {
        cityName: "Cox's Bazar",
        countryName: "Bangladesh",
        countryCode: "BD",
        regionCode: "ASIA",
      },
      analytics: {
        travelers: {
          score: 15,
        },
      },
    },
    {
      type: "location",
      subType: "CITY",
      name: "Sylhet",
      detailedName: "Sylhet, Bangladesh",
      id: "CSYLHET",
      self: {
        href: "https://test.api.amadeus.com/v1/reference-data/locations/CSYLHET",
        methods: ["GET"],
      },
      timeZoneOffset: "+06:00",
      iataCode: "ZYL",
      geoCode: {
        latitude: 24.8949,
        longitude: 91.8687,
      },
      address: {
        cityName: "Sylhet",
        countryName: "Bangladesh",
        countryCode: "BD",
        regionCode: "ASIA",
      },
      analytics: {
        travelers: {
          score: 12,
        },
      },
    },
  ];

  return mockLocations.filter((location) =>
    location.name.toLowerCase().includes(query.toLowerCase())
  );
};

/**
 * Generate mock booking details for development
 * @param {string} confirmationNumber - Confirmation number
 * @returns {Object} Mock booking details
 */
const generateMockBookingDetails = (confirmationNumber) => {
  return {
    data: {
      type: "hotel-booking",
      id: confirmationNumber,
      providerConfirmationId: confirmationNumber,
      associatedRecords: [
        {
          reference: confirmationNumber,
          originSystemCode: "AMADEUS",
        },
      ],
      guests: [
        {
          id: "GT1",
          name: {
            title: "MR",
            firstName: "John",
            lastName: "Doe",
          },
          contact: {
            phone: "+8801234567890",
            email: "<EMAIL>",
          },
        },
      ],
      hotelOffer: {
        hotel: {
          name: "Cox's Bazar Beach Resort",
          address: {
            lines: ["Beach Road"],
            cityName: "Cox's Bazar",
            countryCode: "BD",
          },
        },
        offers: [
          {
            checkInDate: new Date().toISOString().split("T")[0],
            checkOutDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)
              .toISOString()
              .split("T")[0],
            room: {
              description: {
                text: "Superior King Room with Sea View",
              },
            },
            price: {
              currency: "BDT",
              total: "9350.00",
            },
          },
        ],
      },
    },
    confirmationNumber,
    status: "CONFIRMED",
  };
};
