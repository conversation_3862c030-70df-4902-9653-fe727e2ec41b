import React, { useState } from 'react';
import { PassengerForm } from './PassengerForm';
import { PaymentForm } from './PaymentForm';
import '../assets/styles/Booking.css';

const BookingConfirmation = ({ flight, passengerCount }) => {
  const [bookingStep, setBookingStep] = useState('passengers');
  const [passengers, setPassengers] = useState([]);

  const handlePassengersComplete = (passengersData) => {
    setPassengers(passengersData);
    setBookingStep('payment');
  };

  const handlePaymentSuccess = () => {
    // Handle successful payment
    setBookingStep('completed');
  };

  const calculateTotal = () => {
    return flight.price * passengerCount;
  };

  return (
    <div className="booking-container">
      <h2>Confirm Your Booking</h2>
      
      <div className="flight-details">
        <h3>{flight.airline} Flight</h3>
        <p>From: {flight.origin} → To: {flight.destination}</p>
        <p>Departure: {new Date(flight.departure).toLocaleString()}</p>
        {flight.returnDate && (
          <p>Return: {new Date(flight.returnDate).toLocaleString()}</p>
        )}
        <p>Passengers: {passengerCount}</p>
        <p>Price per passenger: ${flight.price}</p>
      </div>

      {bookingStep === 'passengers' && (
        <PassengerForm 
          passengerCount={passengerCount} 
          onComplete={handlePassengersComplete} 
        />
      )}

      {bookingStep === 'payment' && (
        <PaymentForm 
          totalAmount={calculateTotal()} 
          onPaymentSuccess={handlePaymentSuccess} 
        />
      )}

      {bookingStep === 'completed' && (
        <div className="booking-success">
          <h3>Booking Confirmed!</h3>
          <p>Your booking reference is: {Math.random().toString(36).substring(2, 10).toUpperCase()}</p>
        </div>
      )}
    </div>
  );
};

export default BookingConfirmation;
