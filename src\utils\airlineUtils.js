// Import the airlines data
import airlinesData from '../data/airlines.json';

/**
 * Get airline information by IATA code
 * @param {string} iataCode - The IATA code to look up
 * @returns {Object} - Airline information or default object if not found
 */
export const getAirlineByCode = (iataCode) => {
  if (!iataCode) return { 
    name: 'Unknown Airline', 
    logo: "https://cdn-icons-png.flaticon.com/512/187/187820.png" 
  };
  
  try {
    // Search through all regions in the airlines data
    const regions = airlinesData.airlines;
    
    for (const regionKey in regions) {
      const region = regions[regionKey];
      if (region[iataCode]) {
        return region[iataCode];
      }
    }
    
    // If airline not found, return a default with the code as name
    console.warn(`Airline code not found: ${iataCode}`);
    return {
      name: iataCode,
      logo: `https://pics.avs.io/200/200/${iataCode}.png`
    };
  } catch (error) {
    console.error("Error finding airline info:", error);
    return {
      name: iataCode || "Unknown Airline",
      logo: "https://cdn-icons-png.flaticon.com/512/187/187820.png"
    };
  }
};

/**
 * Get all available airlines from the data
 * @returns {Object} - All airlines data organized by region
 */
export const getAllAirlines = () => {
  return airlinesData.airlines;
};
