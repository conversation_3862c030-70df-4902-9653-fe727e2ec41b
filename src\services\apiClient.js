import axios from 'axios';

// Create a custom axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_AMADEUS_API_URL || 'https://test.api.amadeus.com',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token cache
let tokenData = {
  token: null,
  expiresAt: null
};

/**
 * Get an access token for the Amadeus API with caching
 * @returns {Promise<string>} Access token
 */
export const getAccessToken = async () => {
  // Check if we have a valid token
  if (tokenData.token && tokenData.expiresAt && tokenData.expiresAt > Date.now()) {
    return tokenData.token;
  }

  // Get a new token
  try {
    const response = await axios.post(
      `${import.meta.env.VITE_AMADEUS_API_URL || 'https://test.api.amadeus.com'}/v1/security/oauth2/token`,
      new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: import.meta.env.VITE_AMADEUS_API_KEY || '********************************',
        client_secret: import.meta.env.VITE_AMADEUS_API_SECRET || 'qZcGFsMJgCv52UkG',
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    // Calculate expiration time (subtract 5 minutes to be safe)
    const expiresIn = response.data.expires_in * 1000; // Convert to milliseconds
    const expiresAt = Date.now() + expiresIn - 300000; // Subtract 5 minutes

    // Cache the token
    tokenData = {
      token: response.data.access_token,
      expiresAt
    };

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting Amadeus access token:', error);
    throw error;
  }
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    // Skip token for token endpoint
    if (config.url?.includes('/v1/security/oauth2/token')) {
      return config;
    }
    
    const token = await getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle specific error cases
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('API Error Response:', error.response.data);
      
      // Handle token expiration
      if (error.response.status === 401) {
        // Clear token cache to force refresh on next request
        tokenData = { token: null, expiresAt: null };
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('API Error Request:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('API Error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// Cache for API responses
const apiCache = {
  cache: new Map(),
  
  // Get cached response
  get(key) {
    const cachedItem = this.cache.get(key);
    if (!cachedItem) return null;
    
    // Check if cache is expired
    if (cachedItem.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }
    
    return cachedItem.data;
  },
  
  // Set cache with expiration
  set(key, data, ttl = 5 * 60 * 1000) { // Default TTL: 5 minutes
    this.cache.set(key, {
      data,
      expiresAt: Date.now() + ttl
    });
  },
  
  // Clear all cache or specific key
  clear(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
};

/**
 * Make a cached API request
 * @param {Object} config - Axios request config
 * @param {boolean} useCache - Whether to use cache
 * @param {number} cacheTTL - Cache time-to-live in milliseconds
 * @returns {Promise<any>} Response data
 */
export const request = async (config, useCache = false, cacheTTL = 5 * 60 * 1000) => {
  // Generate cache key from request config
  const cacheKey = useCache ? 
    JSON.stringify({
      url: config.url,
      method: config.method || 'get',
      params: config.params,
      data: config.data
    }) : null;
  
  // Check cache if enabled
  if (useCache && cacheKey) {
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached response for:', config.url);
      return cachedData;
    }
  }
  
  // Make the request
  try {
    const response = await apiClient(config);
    
    // Cache the response if enabled
    if (useCache && cacheKey) {
      apiCache.set(cacheKey, response.data, cacheTTL);
    }
    
    return response.data;
  } catch (error) {
    // For development/testing, return mock data if specified
    if (process.env.NODE_ENV !== 'production' && config.mockData) {
      console.warn('Using mock data for failed request:', config.url);
      return config.mockData;
    }
    
    throw error;
  }
};

// Export the cache for direct access if needed
export { apiCache };

export default apiClient;
