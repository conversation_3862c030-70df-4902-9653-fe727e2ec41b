/* Baggage Selection Styles - Updated to match homepage colors */
.baggage-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  );
  min-height: 100vh;
  border-radius: 20px;
}

.passenger-baggage-section {
  border: 2px solid rgba(142, 202, 230, 0.3);
  border-radius: 20px;
  padding: 25px;
  background-color: white;
  box-shadow: 0 10px 30px rgba(2, 48, 71, 0.1);
  margin-bottom: 20px;
}

.passenger-header h5 {
  color: var(--primary-dark);
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
}

.flight-baggage-section {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 20px;
  border: 1px solid rgba(142, 202, 230, 0.2);
  margin-bottom: 15px;
}

.flight-header {
  color: var(--primary-dark);
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--accent-yellow);
  text-align: center;
}

.baggage-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.baggage-option {
  border: 2px solid rgba(142, 202, 230, 0.3);
  border-radius: 15px;
  padding: 20px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-align: center;
  box-shadow: 0 4px 15px rgba(2, 48, 71, 0.1);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.baggage-option:hover {
  border-color: var(--primary-medium);
  box-shadow: 0 8px 25px rgba(2, 48, 71, 0.15);
  transform: translateY(-3px);
}

.baggage-option.selected {
  border-color: var(--accent-yellow);
  background: linear-gradient(
    135deg,
    rgba(255, 183, 3, 0.1),
    rgba(251, 133, 0, 0.1)
  );
  box-shadow: 0 8px 25px rgba(255, 183, 3, 0.2);
}

.baggage-option.recommended {
  border-color: var(--accent-orange);
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(251, 133, 0, 0.05),
    rgba(255, 183, 3, 0.05)
  );
}

.baggage-option.recommended.selected {
  border-color: var(--accent-orange);
  background: linear-gradient(
    135deg,
    rgba(251, 133, 0, 0.15),
    rgba(255, 183, 3, 0.15)
  );
}

.recommended-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(255, 183, 3, 0.3);
}

.baggage-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  color: var(--medium-gray);
}

.baggage-option.selected .baggage-icon {
  color: var(--accent-yellow);
}

.baggage-option.recommended .baggage-icon {
  color: var(--accent-orange);
}

.baggage-details {
  flex-grow: 1;
}

.baggage-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
  font-size: 14px;
}

.baggage-weight {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 5px;
}

.baggage-price {
  font-weight: 700;
  color: #007bff;
  font-size: 16px;
}

.baggage-option.recommended .baggage-price {
  color: #28a745;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #007bff;
  font-size: 18px;
}

.baggage-option.recommended .selected-indicator {
  color: #28a745;
}

/* Responsive Design */
@media (max-width: 768px) {
  .baggage-options-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .baggage-option {
    padding: 12px;
    min-height: 120px;
  }

  .baggage-icon {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  .baggage-label {
    font-size: 13px;
  }

  .baggage-price {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .baggage-options-grid {
    grid-template-columns: 1fr 1fr;
  }

  .passenger-baggage-section {
    padding: 15px;
  }

  .flight-baggage-section {
    padding: 12px;
  }
}

/* Button Improvements */
.baggage-selection-container .btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

.baggage-selection-container .row.gap-2 {
  margin-left: 0;
  margin-right: 0;
}

.baggage-selection-container .row.gap-2 > .col-md-6 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Information Card Styling */
.baggage-selection-container .card {
  border: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.baggage-selection-container .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Animation for selection */
@keyframes selectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.baggage-option.selected {
  animation: selectPulse 0.3s ease-in-out;
}

/* Accessibility improvements */
.baggage-option:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.baggage-option[aria-selected="true"] {
  border-color: #007bff;
  background-color: #e7f3ff;
}

/* High contrast mode support */
@media (forced-colors: active) {
  .baggage-option {
    border: 2px solid ButtonBorder;
  }

  .baggage-option.selected {
    border: 3px solid Highlight;
  }

  .recommended-badge {
    background-color: Highlight;
    color: HighlightText;
  }
}

/* Print styles */
@media print {
  .baggage-selection-container {
    padding: 0;
  }

  .baggage-option {
    border: 1px solid #000;
    break-inside: avoid;
  }

  .baggage-option.selected {
    background-color: #f0f0f0;
  }
}
