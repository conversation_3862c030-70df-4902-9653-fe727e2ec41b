import React, { useState, useEffect } from "react";
import ReactDatePicker from "react-datepicker";
import { useAppContext } from "../context/AppContext";
import "react-datepicker/dist/react-datepicker.css";
import "../assets/styles/DatePicker.css";

/**
 * Enhanced date picker component with improved UX
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Input name
 * @param {Date|string} props.selected - Selected date
 * @param {Function} props.onChange - Change handler function
 * @param {string} props.label - Input label
 * @param {Date|string} props.minDate - Minimum selectable date
 * @param {Date|string} props.maxDate - Maximum selectable date
 * @param {boolean} props.isInvalid - Whether the input is invalid
 * @param {string} props.errorMessage - Error message to display
 * @param {string} props.placeholder - Input placeholder
 * @param {boolean} props.required - Whether the input is required
 * @param {boolean} props.showMonthYearPicker - Whether to show month/year picker
 * @param {boolean} props.showTimeSelect - Whether to show time selection
 * @param {string} props.dateFormat - Date format string
 * @param {boolean} props.isClearable - Whether the input can be cleared
 */
const DatePicker = ({
  name,
  selected,
  onChange,
  label,
  minDate,
  maxDate,
  isInvalid,
  errorMessage,
  placeholder = "Select date",
  required = false,
  showMonthYearPicker = false,
  showTimeSelect = false,
  dateFormat = "MMM d, yyyy",
  isClearable = false,
  ...props
}) => {
  const { state } = useAppContext();
  const isDarkMode = state.theme === "dark";

  // Convert string date to Date object if needed
  const [selectedDate, setSelectedDate] = useState(
    selected
      ? typeof selected === "string"
        ? new Date(selected)
        : selected
      : null
  );

  // Track if calendar is open
  const [isOpen, setIsOpen] = useState(false);

  // Update internal state when selected prop changes
  useEffect(() => {
    if (selected) {
      setSelectedDate(
        typeof selected === "string" ? new Date(selected) : selected
      );
    } else {
      setSelectedDate(null);
    }
  }, [selected]);

  // Handle date change
  const handleChange = (date) => {
    setSelectedDate(date);
    setIsOpen(false);
    // Format date as ISO string for form data if date exists
    // Timezone-safe date formatting
    const formattedDate = date
      ? new Date(date.getTime() - date.getTimezoneOffset() * 60000)
          .toISOString()
          .split("T")[0]
      : "";

    // Call parent onChange with simulated event
    onChange({
      target: {
        name,
        value: formattedDate,
      },
    });
  };

  // Highlight dates
  const getDayClassNames = (date) => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;

    return `${isToday ? "highlighted-today" : ""} ${isWeekend ? "weekend-day" : ""}`.trim();
  };

  // Custom header to improve month/year navigation
  const CustomHeader = ({
    date,
    decreaseMonth,
    increaseMonth,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
  }) => (
    <div className="custom-header">
      <button
        type="button"
        onClick={decreaseMonth}
        disabled={prevMonthButtonDisabled}
        className="navigation-button"
        aria-label="Previous month"
      >
        <i className="fas fa-chevron-left"></i>
      </button>
      <div className="month-year">
        {date.toLocaleString("default", { month: "long", year: "numeric" })}
      </div>
      <button
        type="button"
        onClick={increaseMonth}
        disabled={nextMonthButtonDisabled}
        className="navigation-button"
        aria-label="Next month"
      >
        <i className="fas fa-chevron-right"></i>
      </button>
    </div>
  );

  return (
    <div className="datepicker-container">
      {label && (
        <label
          className="form-label fw-bold"
          htmlFor={`datepicker-${name}`}
          onClick={() => setIsOpen(true)}
        >
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </label>
      )}

      <div
        className={`datepicker-wrapper ${isInvalid ? "is-invalid" : ""} ${isOpen ? "focused" : ""}`}
      >
        <ReactDatePicker
          id={`datepicker-${name}`}
          selected={selectedDate}
          onChange={handleChange}
          minDate={
            minDate
              ? typeof minDate === "string"
                ? new Date(minDate)
                : minDate
              : undefined
          }
          maxDate={
            maxDate
              ? typeof maxDate === "string"
                ? new Date(maxDate)
                : maxDate
              : undefined
          }
          placeholderText={placeholder}
          className={`form-control ${isInvalid ? "is-invalid" : ""}`}
          calendarClassName={`custom-datepicker ${isDarkMode ? "dark-theme" : ""}`}
          showMonthYearPicker={showMonthYearPicker}
          showTimeSelect={showTimeSelect}
          dateFormat={dateFormat}
          isClearable={isClearable}
          dayClassName={getDayClassNames}
          renderCustomHeader={CustomHeader}
          open={isOpen}
          onClickOutside={() => setIsOpen(false)}
          onInputClick={() => setIsOpen(true)}
          popperProps={{
            positionFixed: true,
          }}
          popperModifiers={[
            {
              name: "offset",
              options: {
                offset: [0, 8],
              },
            },
            {
              name: "preventOverflow",
              options: {
                rootBoundary: "viewport",
                padding: 8,
              },
            },
          ]}
          showPopperArrow={false}
          {...props}
        />
        <div
          className="datepicker-icon"
          onClick={() => setIsOpen(true)}
          role="button"
          aria-label="Open calendar"
        >
          <i className="fas fa-calendar-alt"></i>
        </div>
      </div>

      {isInvalid && errorMessage && (
        <div className="invalid-feedback d-block" role="alert">
          {errorMessage}
        </div>
      )}
    </div>
  );
};

export default DatePicker;
