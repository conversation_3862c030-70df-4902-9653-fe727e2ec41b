{"name": "practise-env", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-icons/all-files": "^4.1.0", "axios": "^1.7.9", "bootstrap": "^5.3.5", "express": "^4.21.2", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "prettier": "^3.0.0-alpha.6", "react-router-dom": "^7.1.1", "vite": "^6.3.5"}}