import React, { useEffect } from "react";
import { Routes, Route, useLocation, useNavigate } from "react-router-dom";
import { useAppContext } from "./context/AppContext";
import { BookingStateManager } from "./services/bookingStateManager";
import Navbar from "./features/navigation/Navbar";
import Home from "./pages/Home";
import Flights from "./pages/Flights";
import Hotels from "./pages/Hotels";
import HotelBooking from "./pages/HotelBooking";
import Contact from "./pages/Contact";
import BookingPage from "./pages/BookingPage";
import SessionExpired from "./pages/SessionExpired";
import BookingConfirm from "./Components/BookingConfirm";
import ErrorBoundary from "./Components/ErrorBoundary";
import ApiDiagnostic from "@components/ApiDiagnostic";
import "./App.css";

function App() {
  const location = useLocation();
  const navigate = useNavigate();
  const { state, dispatch } = useAppContext();
  const isHomePage = location.pathname === "/";
  const isSessionExpiredPage = location.pathname === "/session-expired";
  const isBookingFlow = location.pathname.startsWith("/booking");

  // Clear error when location changes
  useEffect(() => {
    if (state.error) {
      dispatch({ type: "CLEAR_ERROR" });
    }
  }, [location.pathname, dispatch, state.error]);

  // Monitor session status and protect booking flow
  useEffect(() => {
    const checkSession = () => {
      const timeSinceLastActivity = Date.now() - state.lastActivity;

      // Handle session expiry
      if (
        timeSinceLastActivity >= state.sessionTimeout &&
        !isSessionExpiredPage
      ) {
        if (isBookingFlow) {
          // Save current state before redirecting
          const currentBookingState = BookingStateManager.getBookingState();
          if (currentBookingState) {
            BookingStateManager.saveLastBookingState(currentBookingState);
          }
        }
        navigate("/session-expired");
      }

      // Show warning before session expires
      if (
        timeSinceLastActivity >= state.sessionWarningTime &&
        !state.showSessionWarning &&
        state.isSessionActive &&
        isBookingFlow
      ) {
        dispatch({ type: "SET_SESSION_WARNING", payload: true });
      }
    };

    const intervalId = setInterval(checkSession, 60000); // Check every minute

    return () => clearInterval(intervalId);
  }, [
    state.lastActivity,
    state.sessionTimeout,
    state.sessionWarningTime,
    state.showSessionWarning,
    state.isSessionActive,
    isSessionExpiredPage,
    isBookingFlow,
    navigate,
    dispatch,
  ]);

  // Update activity on user interaction
  useEffect(() => {
    const handleActivity = () => {
      dispatch({ type: "UPDATE_LAST_ACTIVITY" });
    };

    const activities = [
      "mousemove",
      "keydown",
      "click",
      "scroll",
      "touchstart",
    ];
    activities.forEach((activity) => {
      window.addEventListener(activity, handleActivity);
    });

    return () => {
      activities.forEach((activity) => {
        window.removeEventListener(activity, handleActivity);
      });
    };
  }, [dispatch]);

  return (
    <ErrorBoundary>
      <div className="app">
        <Navbar />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/flights" element={<Flights />} />
            <Route path="/hotels" element={<Hotels />} />
            <Route
              path="/hotel-booking"
              element={
                state.isSessionActive ? <HotelBooking /> : <SessionExpired />
              }
            />
            <Route path="/contact" element={<Contact />} />
            <Route
              path="/booking"
              element={
                state.isSessionActive ? <BookingPage /> : <SessionExpired />
              }
            />
            <Route
              path="/booking-confirm"
              element={
                state.isSessionActive ? <BookingConfirm /> : <SessionExpired />
              }
            />
            <Route path="/session-expired" element={<SessionExpired />} />
            <Route path="/diagnostic" element={<ApiDiagnostic />} />
          </Routes>
        </main>
      </div>
    </ErrorBoundary>
  );
}

export default App;
