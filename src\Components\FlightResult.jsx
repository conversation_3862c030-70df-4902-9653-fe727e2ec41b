import React from "react";
import FlightItem from "./FlightItem";
import "../assets/styles/flightSearch.css";

/**
 * Component for displaying outbound flight search results
 */
function FlightResult({ flights = [], onFlightSelect, selectedFlight }) {
  return (
    <div>
      {flights.length === 0 ? (
        <div className="alert alert-info">No flights found matching your criteria.</div>
      ) : (
        flights.map((flight) => (
          <FlightItem
            key={flight.id}
            flight={flight}
            onSelect={(flight) => onFlightSelect(flight, "outbound")}
            isSelected={selectedFlight?.id === flight.id}
            buttonText="Select"
            selectedButtonText="Selected"
          />
        ))
      )}
    </div>
  );
}

export default FlightResult;
