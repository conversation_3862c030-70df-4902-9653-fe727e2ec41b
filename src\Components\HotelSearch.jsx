import React, { useState, useEffect } from "react";
import { Form, Button, Row, Col, Card, Alert } from "react-bootstrap";
import {
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaUsers,
  FaSearch,
} from "react-icons/fa";
import DatePicker from "react-datepicker";
import {
  searchHotelLocations,
  searchHotelsByCity,
  searchHotelOffers,
} from "../services/hotelService";
import { useAppContext } from "../context/AppContext";
import "react-datepicker/dist/react-datepicker.css";

const HotelSearch = ({ onSearchResults }) => {
  const { state, dispatch } = useAppContext();
  const [formData, setFormData] = useState({
    destination: "",
    checkInDate: new Date(),
    checkOutDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    adults: 1,
    rooms: 1,
  });

  // Debug: Add console log to see if component is rendering
  console.log("HotelSearch component rendering...");
  const [locationSuggestions, setLocationSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState("");

  // Handle destination input change with autocomplete
  const handleDestinationChange = async (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, destination: value }));

    if (value.length > 2) {
      try {
        const suggestions = await searchHotelLocations(value);
        setLocationSuggestions(suggestions);
        setShowSuggestions(true);
      } catch (error) {
        console.error("Location search failed:", error);
        setLocationSuggestions([]);
      }
    } else {
      setLocationSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle location selection
  const handleLocationSelect = (location) => {
    setFormData((prev) => ({
      ...prev,
      destination: location.name,
      destinationCode: location.iataCode || location.id,
    }));
    setShowSuggestions(false);
    setLocationSuggestions([]);
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Validate form data
  const validateForm = () => {
    if (!formData.destination.trim()) {
      setSearchError("Please enter a destination");
      return false;
    }
    if (!formData.checkInDate || !formData.checkOutDate) {
      setSearchError("Please select check-in and check-out dates");
      return false;
    }
    if (formData.checkInDate >= formData.checkOutDate) {
      setSearchError("Check-out date must be after check-in date");
      return false;
    }
    if (formData.checkInDate < new Date().setHours(0, 0, 0, 0)) {
      setSearchError("Check-in date cannot be in the past");
      return false;
    }
    if (formData.adults < 1) {
      setSearchError("At least 1 adult is required");
      return false;
    }
    if (formData.rooms < 1) {
      setSearchError("At least 1 room is required");
      return false;
    }
    return true;
  };

  // Handle hotel search
  const handleSearch = async (e) => {
    e.preventDefault();
    setSearchError("");

    if (!validateForm()) {
      return;
    }

    setIsSearching(true);
    dispatch({ type: "SET_LOADING", payload: true });

    try {
      // First, get hotels in the destination city
      const cityCode = formData.destinationCode || "DAC"; // Default to Dhaka
      const hotels = await searchHotelsByCity({ cityCode });

      if (hotels.length === 0) {
        setSearchError("No hotels found in the selected destination");
        setIsSearching(false);
        dispatch({ type: "SET_LOADING", payload: false });
        return;
      }

      // Get hotel IDs for offers search
      const hotelIds = hotels
        .slice(0, 10)
        .map((hotel) => hotel.hotelId)
        .join(",");

      // Search for hotel offers
      const searchParams = {
        hotelIds,
        checkInDate: formData.checkInDate.toISOString().split("T")[0],
        checkOutDate: formData.checkOutDate.toISOString().split("T")[0],
        adults: formData.adults,
        roomQuantity: formData.rooms,
        currency: "BDT",
      };

      const hotelOffers = await searchHotelOffers(searchParams);

      // Save search to recent searches
      const searchData = {
        ...formData,
        timestamp: new Date().toISOString(),
        type: "hotel",
      };
      dispatch({ type: "ADD_RECENT_SEARCH", payload: searchData });

      // Pass results to parent component
      if (onSearchResults) {
        onSearchResults(hotelOffers, searchParams);
      }
    } catch (error) {
      console.error("Hotel search failed:", error);
      setSearchError("Hotel search failed. Please try again.");
      dispatch({
        type: "SET_ERROR",
        payload: "Hotel search failed. Please try again.",
      });
    } finally {
      setIsSearching(false);
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // Calculate number of nights
  const calculateNights = () => {
    if (formData.checkInDate && formData.checkOutDate) {
      const diffTime = formData.checkOutDate - formData.checkInDate;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }
    return 1;
  };

  return (
    <Card className="hotel-search-card shadow-sm">
      <Card.Header className="bg-primary text-white">
        <h4 className="mb-0">
          <FaSearch className="me-2" />
          Search Hotels
        </h4>
      </Card.Header>
      <Card.Body>
        {searchError && (
          <Alert
            variant="danger"
            dismissible
            onClose={() => setSearchError("")}
          >
            {searchError}
          </Alert>
        )}

        <Form onSubmit={handleSearch}>
          <Row className="g-3">
            {/* Destination */}
            <Col md={6}>
              <Form.Group>
                <Form.Label>
                  <FaMapMarkerAlt className="me-1" />
                  Destination
                </Form.Label>
                <div className="position-relative">
                  <Form.Control
                    type="text"
                    placeholder="Enter city or hotel name"
                    value={formData.destination}
                    onChange={handleDestinationChange}
                    onBlur={() =>
                      setTimeout(() => setShowSuggestions(false), 200)
                    }
                    onFocus={() =>
                      locationSuggestions.length > 0 && setShowSuggestions(true)
                    }
                  />
                  {showSuggestions && locationSuggestions.length > 0 && (
                    <div
                      className="position-absolute w-100 bg-white border rounded shadow-sm"
                      style={{ zIndex: 1000, top: "100%" }}
                    >
                      {locationSuggestions.map((location, index) => (
                        <div
                          key={index}
                          className="p-2 border-bottom cursor-pointer hover-bg-light"
                          onClick={() => handleLocationSelect(location)}
                          style={{ cursor: "pointer" }}
                          onMouseEnter={(e) =>
                            (e.target.style.backgroundColor = "#f8f9fa")
                          }
                          onMouseLeave={(e) =>
                            (e.target.style.backgroundColor = "white")
                          }
                        >
                          <div className="fw-bold">{location.name}</div>
                          <small className="text-muted">
                            {location.detailedName ||
                              location.address?.cityName}
                          </small>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Form.Group>
            </Col>

            {/* Check-in Date */}
            <Col md={3}>
              <Form.Group>
                <Form.Label>
                  <FaCalendarAlt className="me-1" />
                  Check-in
                </Form.Label>
                <DatePicker
                  selected={formData.checkInDate}
                  onChange={(date) => handleInputChange("checkInDate", date)}
                  minDate={new Date()}
                  dateFormat="dd/MM/yyyy"
                  className="form-control"
                  placeholderText="Select check-in date"
                />
              </Form.Group>
            </Col>

            {/* Check-out Date */}
            <Col md={3}>
              <Form.Group>
                <Form.Label>
                  <FaCalendarAlt className="me-1" />
                  Check-out ({calculateNights()} night
                  {calculateNights() !== 1 ? "s" : ""})
                </Form.Label>
                <DatePicker
                  selected={formData.checkOutDate}
                  onChange={(date) => handleInputChange("checkOutDate", date)}
                  minDate={formData.checkInDate || new Date()}
                  dateFormat="dd/MM/yyyy"
                  className="form-control"
                  placeholderText="Select check-out date"
                />
              </Form.Group>
            </Col>

            {/* Adults */}
            <Col md={4}>
              <Form.Group>
                <Form.Label>
                  <FaUsers className="me-1" />
                  Adults
                </Form.Label>
                <Form.Select
                  value={formData.adults}
                  onChange={(e) =>
                    handleInputChange("adults", parseInt(e.target.value))
                  }
                >
                  {[1, 2, 3, 4, 5, 6].map((num) => (
                    <option key={num} value={num}>
                      {num} Adult{num !== 1 ? "s" : ""}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>

            {/* Rooms */}
            <Col md={4}>
              <Form.Group>
                <Form.Label>Rooms</Form.Label>
                <Form.Select
                  value={formData.rooms}
                  onChange={(e) =>
                    handleInputChange("rooms", parseInt(e.target.value))
                  }
                >
                  {[1, 2, 3, 4, 5].map((num) => (
                    <option key={num} value={num}>
                      {num} Room{num !== 1 ? "s" : ""}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>

            {/* Search Button */}
            <Col md={4} className="d-flex align-items-end">
              <Button
                type="submit"
                variant="primary"
                size="lg"
                disabled={isSearching}
                className="w-100"
              >
                {isSearching ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" />
                    Searching...
                  </>
                ) : (
                  <>
                    <FaSearch className="me-2" />
                    Search Hotels
                  </>
                )}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card.Body>
    </Card>
  );
};

export default HotelSearch;
