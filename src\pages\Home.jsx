import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import "../assets/styles/Pages.css";
import "../assets/styles/ModernHome.css";

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    setIsVisible(true);

    // Auto-rotate testimonials
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const featuredDestinations = [
    {
      id: 1,
      name: "Cox's Bazar",
      image: "/images/cox-bazar.jpg",
      description: "World's longest natural sea beach",
      price: "From ৳8,500",
      rating: 4.8,
      activities: "Beach, Water Sports, Sunset",
    },
    {
      id: 2,
      name: "Sundarbans",
      image: "/images/sundarbans.jpg",
      description: "World's largest mangrove forest",
      price: "From ৳12,000",
      rating: 4.6,
      activities: "Wildlife, Boat Safari, Nature",
    },
    {
      id: 3,
      name: "Sylhet",
      image: "/images/sylhet.jpg",
      description: "Land of natural beauty and tea gardens",
      price: "From ৳6,500",
      rating: 4.7,
      activities: "Tea Gardens, Hills, Culture",
    },
  ];

  const services = [
    {
      id: 1,
      icon: "fas fa-plane",
      title: "Flight Booking",
      description:
        "Best deals on domestic and international flights with instant confirmation",
      features: [
        "24/7 Support",
        "Best Prices",
        "Instant Booking",
        "Flexible Dates",
      ],
    },
    {
      id: 2,
      icon: "fas fa-hotel",
      title: "Hotel Booking",
      description: "Luxurious stays at affordable prices across Bangladesh",
      features: [
        "Premium Hotels",
        "Easy Booking",
        "Great Locations",
        "Free Cancellation",
      ],
    },
    {
      id: 3,
      icon: "fas fa-car",
      title: "Transportation",
      description: "Convenient and safe travel across Bangladesh",
      features: [
        "Safe Rides",
        "Professional Drivers",
        "On-time Service",
        "GPS Tracking",
      ],
    },
    {
      id: 4,
      icon: "fas fa-map-marked-alt",
      title: "Tour Packages",
      description: "All-inclusive holiday experiences with expert guides",
      features: [
        "Custom Tours",
        "Expert Guides",
        "All Inclusive",
        "Group Discounts",
      ],
    },
  ];

  const testimonials = [
    {
      id: 1,
      name: "Rahim Ahmed",
      location: "Dhaka",
      rating: 5,
      text: "Tripstar made my vacation planning so easy! The best travel experience I've had. Highly recommended for anyone looking for hassle-free travel.",
      avatar: "/images/avatar-1.jpg",
    },
    {
      id: 2,
      name: "Fatima Khan",
      location: "Chittagong",
      rating: 5,
      text: "Best prices for flights and excellent customer service. The booking process was smooth and the support team was very helpful throughout.",
      avatar: "/images/avatar-2.jpg",
    },
    {
      id: 3,
      name: "Mohammad Hassan",
      location: "Sylhet",
      rating: 5,
      text: "Professional service and amazing tour packages. Will definitely use again! The attention to detail was impressive.",
      avatar: "/images/avatar-3.jpg",
    },
  ];

  const stats = [
    { number: "50K+", label: "Happy Customers" },
    { number: "1000+", label: "Destinations" },
    { number: "24/7", label: "Customer Support" },
    { number: "99%", label: "Satisfaction Rate" },
  ];

  return (
    <div className="home-container">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-background">
          <div className="floating-elements">
            <div className="floating-element plane">✈️</div>
            <div className="floating-element hotel">🏨</div>
            <div className="floating-element palm">🌴</div>
            <div className="floating-element compass">🧭</div>
            <div className="floating-element luggage">🧳</div>
            <div className="floating-element camera">📸</div>
          </div>
        </div>
        <div className={`hero-content ${isVisible ? "fade-in" : ""}`}>
          <div className="hero-badge">🌟 #1 Travel Agency in Bangladesh</div>
          <h1>
            Discover Amazing
            <span className="text-gradient"> Destinations</span>
            <br />
            with <span className="text-gradient">Tripstar</span>
          </h1>
          <p>
            Your gateway to extraordinary adventures across Bangladesh and
            beyond. Experience seamless booking for flights, hotels, and
            complete travel packages with our award-winning service.
          </p>
          <div className="hero-buttons">
            <Link to="/flights" className="btn btn-primary btn-lg">
              <i className="fas fa-plane"></i>
              Book Flights Now
            </Link>
            <Link to="/hotels" className="btn btn-outline btn-lg">
              <i className="fas fa-hotel"></i>
              Find Hotels
            </Link>
          </div>
          <div className="hero-stats">
            {stats.map((stat, index) => (
              <div key={index} className="stat-item">
                <div className="stat-number">{stat.number}</div>
                <div className="stat-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section - Now directly after hero */}
      <section className="services-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Our Premium Services</h2>
            <p className="section-subtitle">
              Everything you need for a perfect journey, all in one place
            </p>
          </div>
          <div className="services-grid">
            {services.map((service) => (
              <div key={service.id} className="service-card modern-card">
                <div className="service-header">
                  <div className="service-icon-container">
                    <i className={service.icon}></i>
                  </div>
                  <h3>{service.title}</h3>
                </div>
                <p>{service.description}</p>
                <ul className="service-features">
                  {service.features.map((feature, index) => (
                    <li key={index}>
                      <i className="fas fa-check"></i>
                      {feature}
                    </li>
                  ))}
                </ul>
                <Link
                  to={`/${service.title.toLowerCase().split(" ")[0]}`}
                  className="service-btn"
                >
                  Explore Now
                  <i className="fas fa-arrow-right"></i>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Destinations */}
      <section className="featured-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Popular Destinations</h2>
            <p className="section-subtitle">
              Discover the most beautiful places Bangladesh has to offer
            </p>
          </div>
          <div className="destinations-grid">
            {featuredDestinations.map((destination) => (
              <div
                key={destination.id}
                className="destination-card modern-card"
              >
                <div className="destination-image">
                  <img src={destination.image} alt={destination.name} />
                  <div className="image-overlay">
                    <div className="price-tag">{destination.price}</div>
                    <div className="rating-badge">
                      <i className="fas fa-star"></i>
                      {destination.rating}
                    </div>
                  </div>
                </div>
                <div className="destination-content">
                  <h3>{destination.name}</h3>
                  <p>{destination.description}</p>
                  <div className="destination-activities">
                    <i className="fas fa-map-marker-alt"></i>
                    {destination.activities}
                  </div>
                  <Link
                    to={`/destinations/${destination.id}`}
                    className="btn btn-secondary destination-btn"
                  >
                    <i className="fas fa-eye"></i>
                    Explore Details
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="why-choose-section">
        <div className="section-container">
          <div className="section-header">
            <h2>Why Choose Tripstar?</h2>
            <p className="section-subtitle">
              We're committed to making your travel dreams come true
            </p>
          </div>
          <div className="features-grid">
            <div className="feature-item modern-card">
              <div className="feature-icon-container">
                <i className="fas fa-shield-alt"></i>
              </div>
              <h3>Best Price Guarantee</h3>
              <p>
                We offer the most competitive rates in the market with no hidden
                fees
              </p>
            </div>
            <div className="feature-item modern-card">
              <div className="feature-icon-container">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>Verified Properties</h3>
              <p>
                All our listings are personally verified for quality and
                authenticity
              </p>
            </div>
            <div className="feature-item modern-card">
              <div className="feature-icon-container">
                <i className="fas fa-headset"></i>
              </div>
              <h3>24/7 Support</h3>
              <p>
                Round-the-clock assistance for all your travel needs and
                questions
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="testimonials-section">
        <div className="section-container">
          <div className="section-header">
            <h2>What Our Travelers Say</h2>
            <p className="section-subtitle">
              Real experiences from real travelers who chose Tripstar
            </p>
          </div>
          <div className="testimonial-carousel">
            <div className="testimonial-card active">
              <div className="testimonial-content">
                <div className="testimonial-rating">
                  {[...Array(testimonials[currentTestimonial].rating)].map(
                    (_, i) => (
                      <i key={i} className="fas fa-star"></i>
                    )
                  )}
                </div>
                <p>"{testimonials[currentTestimonial].text}"</p>
                <div className="testimonial-author">
                  <img
                    src={testimonials[currentTestimonial].avatar}
                    alt={testimonials[currentTestimonial].name}
                    onError={(e) => {
                      e.target.src = "/images/default-avatar.jpg";
                    }}
                  />
                  <div className="author-info">
                    <div className="author-name">
                      {testimonials[currentTestimonial].name}
                    </div>
                    <div className="author-location">
                      <i className="fas fa-map-marker-alt"></i>
                      {testimonials[currentTestimonial].location}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="testimonial-navigation">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={`nav-dot ${index === currentTestimonial ? "active" : ""}`}
                  onClick={() => setCurrentTestimonial(index)}
                ></button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="newsletter-section">
        <div className="section-container">
          <div className="newsletter-content">
            <div className="newsletter-icon">
              <i className="fas fa-paper-plane"></i>
            </div>
            <h2>Stay Updated with Travel Deals</h2>
            <p>
              Subscribe to our newsletter and get exclusive offers, travel tips,
              and destination guides delivered to your inbox
            </p>
            <form className="newsletter-form">
              <div className="form-group-inline">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  required
                />
                <button type="submit" className="btn btn-primary">
                  <i className="fas fa-paper-plane"></i>
                  Subscribe Now
                </button>
              </div>
            </form>
            <div className="newsletter-benefits">
              <div className="benefit">
                <i className="fas fa-tag"></i>
                Exclusive Deals
              </div>
              <div className="benefit">
                <i className="fas fa-map"></i>
                Travel Tips
              </div>
              <div className="benefit">
                <i className="fas fa-bell"></i>
                Early Access
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="section-container">
          <div className="cta-content">
            <h2>Ready to Start Your Journey?</h2>
            <p>
              Join thousands of happy travelers who trust Tripstar for their
              adventures
            </p>
            <Link to="/flights" className="btn btn-primary btn-lg cta-btn">
              <i className="fas fa-plane"></i>
              Start Planning Your Trip
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
