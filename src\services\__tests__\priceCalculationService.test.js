import { PriceCalculationService } from "../priceCalculationService";

describe("PriceCalculationService", () => {
  const mockFlights = {
    outbound: {
      price: { total: 1000, currency: "BDT" },
    },
    return: {
      price: { total: 1000, currency: "BDT" },
    },
  };

  describe("calculateBasePrice", () => {
    test("should calculate base price correctly for one-way flight", () => {
      const oneWayFlight = { outbound: mockFlights.outbound };
      expect(PriceCalculationService.calculateBasePrice(oneWayFlight)).toBe(
        1000
      );
    });

    test("should calculate base price correctly for round trip", () => {
      expect(PriceCalculationService.calculateBasePrice(mockFlights)).toBe(
        2000
      );
    });

    test("should return 0 for invalid flight data", () => {
      expect(PriceCalculationService.calculateBasePrice(null)).toBe(0);
      expect(PriceCalculationService.calculateBasePrice({})).toBe(0);
    });
  });

  describe("calculatePassengerTypePrice", () => {
    const basePrice = 1000;

    test("should calculate adult price correctly", () => {
      expect(
        PriceCalculationService.calculatePassengerTypePrice(
          basePrice,
          "adult",
          1
        )
      ).toBe(1000);
      expect(
        PriceCalculationService.calculatePassengerTypePrice(
          basePrice,
          "adult",
          2
        )
      ).toBe(2000);
    });

    test("should calculate child price correctly", () => {
      expect(
        PriceCalculationService.calculatePassengerTypePrice(
          basePrice,
          "child",
          1
        )
      ).toBe(750); // 75% of adult price
    });

    test("should calculate infant price correctly", () => {
      expect(
        PriceCalculationService.calculatePassengerTypePrice(
          basePrice,
          "infant",
          1
        )
      ).toBe(100); // 10% of adult price
    });
  });

  describe("calculateSeatPrice", () => {
    test("should calculate premium seat prices correctly", () => {
      const price = PriceCalculationService.calculateSeatPrice(
        3,
        0,
        "standard"
      );
      expect(price).toBe(1000); // Premium row (1-5)
    });

    test("should calculate exit row seat prices correctly", () => {
      const price = PriceCalculationService.calculateSeatPrice(
        13,
        0,
        "standard"
      );
      expect(price).toBe(800); // Exit row (12-14)
    });

    test("should add window seat premium", () => {
      const price = PriceCalculationService.calculateSeatPrice(
        20,
        0,
        "standard"
      );
      expect(price).toBe(700); // Standard (500) + Window premium (200)
    });

    test("should apply pricing option discounts", () => {
      const basePrice = PriceCalculationService.calculateSeatPrice(
        1,
        1,
        "basic"
      );
      const premiumPrice = PriceCalculationService.calculateSeatPrice(
        1,
        1,
        "premium"
      );
      expect(premiumPrice).toBe(basePrice * 0.8); // 20% discount for premium
    });
  });

  describe("calculateTotalPrice", () => {
    const testData = {
      flights: mockFlights,
      passengers: {
        adult: 2,
        child: 1,
        infant: 1,
      },
      pricingOption: "standard",
      seatSelections: {
        outbound: {
          "1A": "passenger-1",
          "1B": "passenger-2",
          "1C": "passenger-3",
        },
        return: {
          "2A": "passenger-1",
          "2B": "passenger-2",
          "2C": "passenger-3",
        },
      },
    };

    test("should calculate total price with all components", () => {
      const result = PriceCalculationService.calculateTotalPrice(testData);
      expect(result.basePrice).toBe(2000);
      expect(result.passengerTotal).toBeGreaterThan(0);
      expect(result.seatTotal).toBeGreaterThan(0);
      expect(result.total).toBeGreaterThan(result.basePrice);
    });

    test("should apply fare multipliers correctly", () => {
      const basicPrice = PriceCalculationService.calculateTotalPrice({
        ...testData,
        pricingOption: "basic",
      }).total;

      const standardPrice = PriceCalculationService.calculateTotalPrice({
        ...testData,
        pricingOption: "standard",
      }).total;

      expect(standardPrice).toBe(basicPrice * 1.1); // 10% more than basic
    });
  });

  describe("calculatePromotionalDiscount", () => {
    test("should apply percentage discount correctly", () => {
      const result = PriceCalculationService.calculatePromotionalDiscount(
        1000,
        "WELCOME2025"
      );
      expect(result.discountAmount).toBe(100); // 10% of 1000
      expect(result.isValid).toBe(true);
    });

    test("should apply fixed discount correctly", () => {
      const result = PriceCalculationService.calculatePromotionalDiscount(
        2000,
        "FAMILYTRIP"
      );
      expect(result.discountAmount).toBe(1000);
      expect(result.isValid).toBe(true);
    });

    test("should handle expired promo codes", () => {
      // Mock current date to test expiry
      const realDate = Date;
      global.Date = class extends Date {
        constructor(...args) {
          if (args.length) {
            return new realDate(...args);
          }
          return new realDate("2026-01-01"); // Date after EARLYBIRD expiry
        }
      };

      const result = PriceCalculationService.calculatePromotionalDiscount(
        1000,
        "EARLYBIRD"
      );
      expect(result.isValid).toBe(false);
      expect(result.message).toBe("Promotional code has expired");

      global.Date = realDate; // Restore original Date
    });

    test("should handle invalid promo codes", () => {
      const result = PriceCalculationService.calculatePromotionalDiscount(
        1000,
        "INVALID"
      );
      expect(result.isValid).toBe(false);
      expect(result.discountAmount).toBe(0);
    });
  });

  describe("calculateTotalPrice with promotions", () => {
    const testData = {
      flights: mockFlights,
      passengers: {
        adult: 2,
        child: 1,
        infant: 1,
      },
      pricingOption: "standard",
      seatSelections: {
        outbound: {
          "1A": "passenger-1",
          "1B": "passenger-2",
        },
      },
      promoCode: "WELCOME2025",
    };

    test("should calculate total with promotional discount", () => {
      const result = PriceCalculationService.calculateTotalPrice(testData);
      expect(result.promoDiscount).toBeGreaterThan(0);
      expect(result.total).toBeLessThan(result.subtotal);
      expect(result.isPromoValid).toBe(true);
    });

    test("should include promotional message", () => {
      const result = PriceCalculationService.calculateTotalPrice(testData);
      expect(result.promoMessage).toContain("10%");
    });

    test("should handle calculation without promo code", () => {
      const result = PriceCalculationService.calculateTotalPrice({
        ...testData,
        promoCode: undefined,
      });
      expect(result.promoDiscount).toBe(0);
      expect(result.total).toBe(result.subtotal);
      expect(result.isPromoValid).toBe(false);
    });
  });
});
