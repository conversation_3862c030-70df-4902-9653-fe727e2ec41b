# 🚀 Vercel Deployment Troubleshooting Guide

## ❌ Error: "Failed to fetch flight data. Please try again."

This error occurs when the Amadeus API credentials are not properly configured in your Vercel deployment environment.

## 🔧 **Solution Steps**

### **Step 1: Configure Environment Variables in Vercel**

1. **Access Vercel Dashboard**
   - Go to [vercel.com](https://vercel.com)
   - Sign in to your account
   - Navigate to your `tripthree` project

2. **Add Environment Variables**
   - Click on **Settings** tab
   - Click on **Environment Variables** in the left sidebar
   - Add the following variables:

   ```
   Name: VITE_AMADEUS_API_KEY
   Value: ********************************
   Environment: Production, Preview, Development (select all)
   ```

   ```
   Name: VITE_AMADEUS_API_SECRET
   Value: aE2SAYov8SuJpI0n
   Environment: Production, Preview, Development (select all)
   ```

   ```
   Name: VITE_AMADEUS_API_URL
   Value: https://test.api.amadeus.com
   Environment: Production, Preview, Development (select all)
   ```

3. **Save and Redeploy**
   - Click **Save** for each variable
   - Go to **Deployments** tab
   - Click the **three dots** on your latest deployment
   - Select **Redeploy**

### **Step 2: Verify Configuration**

After redeployment, you can verify the configuration by:

1. **Visit the diagnostic page**: `https://your-app.vercel.app/diagnostic`
2. **Click "Check Environment"** to see if variables are loaded
3. **Click "Test API Connection"** to verify API access

### **Step 3: Alternative Verification**

If you can't access the diagnostic page, check the browser console:

1. Open your deployed app
2. Press **F12** to open Developer Tools
3. Go to **Console** tab
4. Try to search for flights
5. Look for error messages that might indicate:
   - Missing environment variables
   - API authentication failures
   - Network connectivity issues

## 🔍 **Common Issues and Solutions**

### **Issue 1: Environment Variables Not Loading**

**Symptoms:**
- Diagnostic shows "Not Set" for API credentials
- Console shows `undefined` for environment variables

**Solution:**
- Ensure variable names are exactly: `VITE_AMADEUS_API_KEY`, `VITE_AMADEUS_API_SECRET`, `VITE_AMADEUS_API_URL`
- Make sure there are no extra spaces in variable names or values
- Verify you selected all environments (Production, Preview, Development)
- Redeploy after adding variables

### **Issue 2: API Authentication Failure**

**Symptoms:**
- Environment variables are set but API calls fail
- Error: "Invalid client credentials" or similar

**Solution:**
- Verify your Amadeus API credentials are correct
- Check if your API key is active in the Amadeus developer portal
- Ensure you're using the correct API URL (test vs production)

### **Issue 3: CORS Issues**

**Symptoms:**
- Error: "CORS policy" or "Access-Control-Allow-Origin"

**Solution:**
- This is normal for client-side API calls to external services
- Consider implementing a backend proxy if needed
- For development, the current setup should work

### **Issue 4: Rate Limiting**

**Symptoms:**
- API works sometimes but fails frequently
- Error: "Rate limit exceeded"

**Solution:**
- Amadeus test API has rate limits
- Implement request throttling
- Consider caching responses
- Upgrade to production API for higher limits

## 🛠️ **Build Configuration**

Ensure your `vite.config.js` is properly configured:

```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/Components'),
      '@features': path.resolve(__dirname, './src/features'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['bootstrap', '@popperjs/core'],
        },
      },
    },
  },
  server: {
    port: 5173,
    host: true,
  },
})
```

## 📋 **Deployment Checklist**

Before deploying, ensure:

- [ ] All environment variables are set in Vercel
- [ ] API credentials are valid and active
- [ ] Build completes successfully locally (`npm run build`)
- [ ] No console errors in local development
- [ ] All imports use correct paths (case-sensitive)
- [ ] No hardcoded localhost URLs

## 🔄 **Redeployment Process**

If you make changes to environment variables:

1. **Automatic Redeployment** (Recommended)
   - Push changes to your GitHub repository
   - Vercel will automatically redeploy

2. **Manual Redeployment**
   - Go to Vercel Dashboard → Deployments
   - Click three dots on latest deployment
   - Select "Redeploy"

## 📞 **Getting Help**

If issues persist:

1. **Check Vercel Logs**
   - Go to Deployments → Click on deployment → View Function Logs
   - Look for build or runtime errors

2. **Check Browser Console**
   - Open Developer Tools (F12)
   - Look for JavaScript errors or network failures

3. **Test API Credentials**
   - Use the diagnostic tool at `/diagnostic`
   - Verify credentials work in Postman or similar tool

4. **Contact Support**
   - Vercel Support: [vercel.com/support](https://vercel.com/support)
   - Amadeus Support: [developers.amadeus.com](https://developers.amadeus.com)

## 🎯 **Quick Fix Commands**

If you need to quickly redeploy:

```bash
# From your local project directory
git add .
git commit -m "fix: Update environment configuration"
git push origin main
```

This will trigger an automatic redeployment on Vercel.

## ✅ **Success Indicators**

Your deployment is working correctly when:

- [ ] Flight search returns results without errors
- [ ] Diagnostic page shows all environment variables as "Set"
- [ ] API connection test succeeds
- [ ] No console errors related to API calls
- [ ] All features work as expected in production

Remember: Environment variables in Vercel are separate from your local `.env` file and must be configured through the Vercel dashboard.
