import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import "../../assets/styles/flightSearch.css";
import "../../assets/styles/ModernFlightSearch.css";
import FlightResult from "@components/FlightResult";
import ReturnFlightResult from "@components/ReturnFlightResult";
import PricingSelect from "@features/booking/PricingSelect";
import AirportAutocomplete from "../../Components/AirportAutocomplete";
import MultiCityInput from "../../Components/MultiCityInput";
import FilterPanel from "../../Components/FilterPanel";
import FlexibleDateSearch from "../../Components/FlexibleDateSearch";
import DatePicker from "../../Components/DatePicker";
import DateRangePicker from "../../Components/DateRangePicker";
import { getAirlineByCode } from "../../utils/airlineUtils";
// bootstrap modal
import { Modal } from "react-bootstrap";
// font awesome

const FlightSearch = () => {
  // Form Data State
  // Update the initial formData state
  const [formData, setFormData] = useState({
    origin: "",
    destination: "",
    departure: "",
    returnDate: "",
    passengers: 1,
    children: 0,
    infants: 0,
    cabinClass: "ECONOMY",
  });
  // Flexible date search state
  const [flexibleDates, setFlexibleDates] = useState({
    enabled: false,
    days: 3,
    selectedDate: null,
    availableFlights: [],
  });

  // Flight Data State
  const [flights, setFlights] = useState([]);
  const [returnFlights, setReturnFlights] = useState([]);
  const [dateMatrix, setDateMatrix] = useState([]);

  // Selection State
  const [selectedFlights, setSelectedFlights] = useState({
    outbound: null,
    return: null,
  });

  // UI State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [tripType, setTripType] = useState("round"); // 'one-way', 'round', or 'multi-city'
  const [showSearchForm, setShowSearchForm] = useState(true);

  // Ref for scrolling to results
  const resultsRef = useRef(null);

  // Multi-city state
  const [multiCitySegments, setMultiCitySegments] = useState([
    { origin: "", destination: "", date: "" },
    { origin: "", destination: "", date: "" },
  ]);
  const [multiCityErrors, setMultiCityErrors] = useState([]);

  // Sorting and filter states
  const [advancedFilters, setAdvancedFilters] = useState({
    airlines: [],
    stops: [],
    departureTime: [],
    arrivalTime: [],
    duration: { min: "", max: "" },
  });
  const [availableAirlines, setAvailableAirlines] = useState([]);

  // Form Validation State
  const [formErrors, setFormErrors] = useState({
    origin: "",
    destination: "",
    departure: "",
    returnDate: "",
    passengers: "",
  });

  // Flight Select Handler
  const handleFlightSelect = (flight, type) => {
    const newSelection = {
      ...selectedFlights,
      [type]: flight,
    };
    setSelectedFlights(newSelection);

    // Only show modal for one-way trips or when both flights are selected
    if (
      tripType === "one-way" ||
      (newSelection.outbound && newSelection.return)
    ) {
      setShowPricingModal(true);
    }
  };

  // Form Submit Handler
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    setLoading(true);
    setError("");
    setFlights([]);
    setReturnFlights([]);
    setSelectedFlights({ outbound: null, return: null });

    try {
      const tokenResponse = await axios.post(
        `${
          import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"
        }/v1/security/oauth2/token`,
        new URLSearchParams({
          grant_type: "client_credentials",
          client_id: import.meta.env.VITE_AMADEUS_API_KEY,
          client_secret: import.meta.env.VITE_AMADEUS_API_SECRET,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const accessToken = tokenResponse.data.access_token;

      if (tripType === "multi-city") {
        // Handle multi-city search
        // Note: Amadeus doesn't directly support multi-city in one call
        // We'll need to make separate calls for each segment

        const allFlights = [];

        for (let i = 0; i < multiCitySegments.length; i++) {
          const segment = multiCitySegments[i];

          const segmentParams = {
            originLocationCode: segment.origin.toUpperCase(),
            destinationLocationCode: segment.destination.toUpperCase(),
            departureDate: segment.date,
            adults: formData.passengers,
            children: formData.children || undefined,
            infants: formData.infants || undefined,
            max: 5,
            currencyCode: "BDT",
            travelClass: formData.cabinClass,
          };

          try {
            const segmentResponse = await axios.get(
              `${
                import.meta.env.VITE_AMADEUS_API_URL ||
                "https://test.api.amadeus.com"
              }/v2/shopping/flight-offers`,
              {
                headers: { Authorization: `Bearer ${accessToken}` },
                params: segmentParams,
              }
            );

            // Add segment info to each flight
            const segmentFlights = segmentResponse.data?.data || [];
            segmentFlights.forEach((flight) => {
              flight.segmentIndex = i;
              flight.segmentInfo = {
                origin: segment.origin,
                destination: segment.destination,
                date: segment.date,
              };
            });

            allFlights.push(...segmentFlights);
          } catch (err) {
            console.error(`Error fetching segment ${i + 1}:`, err);
            setError(
              `Failed to fetch flights for segment ${i + 1}. Please try again.`
            );
          }
        }

        setFlights(allFlights);
      } else {
        // Handle one-way or round trip search
        if (tripType !== "multi-city") {
          const outboundParams = {
            originLocationCode: formData.origin.toUpperCase(),
            destinationLocationCode: formData.destination.toUpperCase(),
            departureDate: formData.departure,
            adults: formData.passengers,
            children: formData.children || undefined,
            infants: formData.infants || undefined,
            max: 5,
            currencyCode: "BDT",
            travelClass: formData.cabinClass,
          };

          if (flexibleDates.enabled) {
            try {
              // If a date is already selected from the matrix, use those flights
              if (
                flexibleDates.selectedDate &&
                flexibleDates.availableFlights
              ) {
                setFlights(flexibleDates.availableFlights);
              } else {
                // Calculate dates around the departure date
                const baseDepartureDate = new Date(formData.departure);
                const allOutboundFlights = [];

                // Search for flights on each day in the range
                for (
                  let dayOffset = -flexibleDates.days;
                  dayOffset <= flexibleDates.days;
                  dayOffset++
                ) {
                  const currentDate = new Date(baseDepartureDate);
                  currentDate.setDate(baseDepartureDate.getDate() + dayOffset);
                  const formattedDate = currentDate.toISOString().split("T")[0];

                  try {
                    const flexParams = {
                      ...outboundParams,
                      departureDate: formattedDate,
                    };

                    const flexResponse = await axios.get(
                      `${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v2/shopping/flight-offers`,
                      {
                        headers: { Authorization: `Bearer ${accessToken}` },
                        params: flexParams,
                      }
                    );

                    // Add date info to each flight
                    const dateFlights = flexResponse.data?.data || [];
                    dateFlights.forEach((flight) => {
                      flight.flexDate = {
                        original: formData.departure,
                        actual: formattedDate,
                        dayDiff: dayOffset,
                      };
                    });

                    allOutboundFlights.push(...dateFlights);
                  } catch (err) {
                    console.error(
                      `Error fetching flights for date offset ${dayOffset}:`,
                      err
                    );
                  }
                }

                setFlights(allOutboundFlights);
              }

              // Handle flexible dates for return flight
              if (tripType === "round" && formData.returnDate) {
                const returnParams = {
                  originLocationCode: formData.destination.toUpperCase(),
                  destinationLocationCode: formData.origin.toUpperCase(),
                  departureDate: formData.returnDate,
                  adults: formData.passengers,
                  children: formData.children || undefined,
                  infants: formData.infants || undefined,
                  max: 5,
                  currencyCode: "BDT",
                  travelClass: formData.cabinClass,
                };

                const baseReturnDate = new Date(formData.returnDate);
                const allReturnFlights = [];

                // Search for return flights on each day in the range
                for (
                  let dayOffset = -flexibleDates.days;
                  dayOffset <= flexibleDates.days;
                  dayOffset++
                ) {
                  const currentDate = new Date(baseReturnDate);
                  currentDate.setDate(baseReturnDate.getDate() + dayOffset);
                  const formattedDate = currentDate.toISOString().split("T")[0];

                  try {
                    const flexReturnParams = {
                      ...returnParams,
                      departureDate: formattedDate,
                    };

                    const flexReturnResponse = await axios.get(
                      `${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v2/shopping/flight-offers`,
                      {
                        headers: { Authorization: `Bearer ${accessToken}` },
                        params: flexReturnParams,
                      }
                    );

                    // Add date info to each flight
                    const dateFlights = flexReturnResponse.data?.data || [];
                    dateFlights.forEach((flight) => {
                      flight.flexDate = {
                        original: formData.returnDate,
                        actual: formattedDate,
                        dayDiff: dayOffset,
                      };
                    });

                    allReturnFlights.push(...dateFlights);
                  } catch (err) {
                    console.error(
                      `Error fetching return flights for date offset ${dayOffset}:`,
                      err
                    );
                  }
                }

                setReturnFlights(allReturnFlights);
              }
            } catch (error) {
              console.error("Flexible date search failed:", error);
              setError(
                "Failed to fetch flexible date options. Please try again."
              );
              setLoading(false);
              return;
            }
          } else {
            // Standard non-flexible search
            const outboundResponse = await axios.get(
              `${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v2/shopping/flight-offers`,
              {
                headers: { Authorization: `Bearer ${accessToken}` },
                params: outboundParams,
              }
            );

            setFlights(outboundResponse.data?.data || []);

            if (tripType === "round" && formData.returnDate) {
              const returnParams = {
                originLocationCode: formData.destination.toUpperCase(),
                destinationLocationCode: formData.origin.toUpperCase(),
                departureDate: formData.returnDate,
                adults: formData.passengers,
                children: formData.children || undefined,
                infants: formData.infants || undefined,
                max: 5,
                currencyCode: "BDT",
                travelClass: formData.cabinClass,
              };

              const returnResponse = await axios.get(
                `${import.meta.env.VITE_AMADEUS_API_URL || "https://test.api.amadeus.com"}/v2/shopping/flight-offers`,
                {
                  headers: { Authorization: `Bearer ${accessToken}` },
                  params: returnParams,
                }
              );
              setReturnFlights(returnResponse.data?.data || []);
            }
          }
        }
      }

      setLoading(false); // Success loading
      setShowSearchForm(false);
    } catch (err) {
      setError("Failed to fetch flight data. Please try again.");
      console.error("API Error:", err.response?.data || err.message);
      setLoading(false);
    }
  };

  // Form Validation Function
  const validateForm = () => {
    const errors = {};

    if (tripType === "multi-city") {
      // Validate multi-city segments
      const segmentErrors = [];
      let hasErrors = false;

      multiCitySegments.forEach((segment, index) => {
        const segmentError = {};

        // Validate origin and destination (must be 3-letter IATA codes)
        if (!segment.origin || !segment.origin.match(/^[A-Z]{3}$/)) {
          segmentError.origin =
            "Please enter a valid airport or city code (3 letters)";
          hasErrors = true;
        }

        if (!segment.destination || !segment.destination.match(/^[A-Z]{3}$/)) {
          segmentError.destination =
            "Please enter a valid airport or city code (3 letters)";
          hasErrors = true;
        }

        // Check if origin and destination are the same
        if (
          segment.origin &&
          segment.destination &&
          segment.origin === segment.destination
        ) {
          segmentError.destination =
            "Origin and destination cannot be the same";
          hasErrors = true;
        }

        // Validate date
        if (!segment.date) {
          segmentError.date = "Please select a departure date";
          hasErrors = true;
        }

        segmentErrors[index] =
          Object.keys(segmentError).length > 0 ? segmentError : null;
      });

      setMultiCityErrors(segmentErrors);
      return !hasErrors;
    } else {
      // Validate standard form (one-way or round trip)

      // Validate origin and destination (must be 3-letter IATA codes)
      if (!formData.origin || !formData.origin.match(/^[A-Z]{3}$/))
        errors.origin = "Please enter a valid airport or city code (3 letters)";

      if (!formData.destination || !formData.destination.match(/^[A-Z]{3}$/))
        errors.destination =
          "Please enter a valid airport or city code (3 letters)";

      // Check if origin and destination are the same
      if (
        formData.origin &&
        formData.destination &&
        formData.origin === formData.destination
      ) {
        errors.destination = "Origin and destination cannot be the same";
      }

      // Validate departure date
      if (!formData.departure)
        errors.departure = "Please select a departure date";
      else if (new Date(formData.departure) < new Date().setHours(0, 0, 0, 0))
        errors.departure = "Departure date cannot be in the past";

      // Validate return date for round trips
      if (tripType === "round" && formData.returnDate) {
        if (new Date(formData.returnDate) < new Date(formData.departure))
          errors.returnDate = "Return date must be after departure date";
      }

      // Validate passenger count
      if (formData.passengers < 1 || formData.passengers > 9)
        errors.passengers = "1-9 passengers allowed";

      // Enhanced infant validation
      if (formData.infants > 0) {
        // Validate infant-to-adult ratio (1:1)
        if (formData.infants > formData.passengers) {
          errors.infants =
            "Each infant must be accompanied by an adult (1:1 ratio)";
        }

        // Validate maximum infants
        const maxInfants = Math.min(4, formData.passengers); // Maximum 4 infants per booking
        if (formData.infants > maxInfants) {
          errors.infants = `Maximum ${maxInfants} infants allowed for ${formData.passengers} adult passengers`;
        }

        // Additional validation for business/first class
        if (
          formData.cabinClass === "BUSINESS" ||
          formData.cabinClass === "FIRST"
        ) {
          // Some airlines have restrictions on infants in premium cabins
          if (formData.infants > Math.floor(formData.passengers / 2)) {
            errors.infants = `For ${formData.cabinClass.toLowerCase()} class, maximum 1 infant per 2 adults is allowed`;
          }
        }
      }

      setFormErrors(errors);
      return Object.keys(errors).length === 0;
    }
  };

  // Input Change Handler
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    // Convert passenger counts to numbers
    const updatedValue = ["passengers", "children", "infants"].includes(name)
      ? parseInt(value, 10) || 0 // Use parseInt and default to 0 if parsing fails
      : value;
    setFormData({ ...formData, [name]: updatedValue });
  };

  // Close Pricing Modal Handler
  const handleClosePricing = () => {
    setShowPricingModal(false);
  };

  // Toggle trip type
  const handleTripTypeChange = (type) => {
    setTripType(type);
    if (type === "one-way") {
      setFormData({ ...formData, returnDate: "" });
    }

    // Reset errors when changing trip type
    setFormErrors({});
    setMultiCityErrors([]);
  };

  // Multi-city handlers
  const handleMultiCityChange = (updatedSegments) => {
    setMultiCitySegments(updatedSegments);
  };

  const handleAddSegment = () => {
    if (multiCitySegments.length < 5) {
      setMultiCitySegments([
        ...multiCitySegments,
        { origin: "", destination: "", date: "" },
      ]);
    }
  };

  const handleRemoveSegment = (index) => {
    if (multiCitySegments.length > 2) {
      const updatedSegments = [...multiCitySegments];
      updatedSegments.splice(index, 1);
      setMultiCitySegments(updatedSegments);
    }
  };

  // Swap origin and destination
  const handleSwapLocations = () => {
    setFormData({
      ...formData,
      origin: formData.destination,
      destination: formData.origin,
    });
  };

  // We no longer need to preload airline data as it's handled by the airlineUtils module

  // Extract unique airlines from flight data
  useEffect(() => {
    if (flights.length > 0) {
      const airlines = new Set();

      flights.forEach((flight) => {
        flight.itineraries.forEach((itinerary) => {
          itinerary.segments.forEach((segment) => {
            if (segment.carrierCode) {
              airlines.add(segment.carrierCode);
            }
          });
        });
      });

      // Convert to array of objects with code and name using our utility function
      const airlineList = Array.from(airlines).map((code) => {
        const airlineInfo = getAirlineByCode(code);
        return {
          code,
          name: airlineInfo.name,
        };
      });

      setAvailableAirlines(airlineList);
    }
  }, [flights]);

  // Scroll to results when flights are loaded and form is hidden
  useEffect(() => {
    if ((flights.length > 0 || returnFlights.length > 0) && !showSearchForm) {
      resultsRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, [flights, returnFlights, showSearchForm]);

  // Handle advanced filter changes
  const handleAdvancedFilterChange = (filters) => {
    setAdvancedFilters(filters);
  };
  // Handle flexible date changes
  const handleFlexibleDateChange = async (flexSettings) => {
    setFlexibleDates(flexSettings);

    // If a specific date was selected from the matrix
    if (flexSettings.selectedDate && flexSettings.availableFlights) {
      setFlights(flexSettings.availableFlights);
      setFormData((prev) => ({
        ...prev,
        departure: flexSettings.selectedDate,
      }));
    }
  };

  /**
   * Applies sorting and filtering to flight list
   * @param {Array} flightList - Original list of flights
   * @returns {Array} - Filtered and sorted flight list
   */
  const handleSortAndFilter = (flightList) => {
    let filteredFlights = [...flightList];

    // Apply advanced filters
    if (advancedFilters.airlines.length > 0) {
      filteredFlights = filteredFlights.filter((flight) => {
        // Check if any segment's carrier is in the selected airlines
        return flight.itineraries.some((itinerary) =>
          itinerary.segments.some((segment) =>
            advancedFilters.airlines.includes(segment.carrierCode)
          )
        );
      });
    }

    if (advancedFilters.stops.length > 0) {
      filteredFlights = filteredFlights.filter((flight) => {
        return flight.itineraries.some((itinerary) => {
          const stopCount = itinerary.segments.length - 1;
          // For 2+ stops, check if stopCount >= 2
          return advancedFilters.stops.some((stop) =>
            stop === 2 ? stopCount >= 2 : stopCount === stop
          );
        });
      });
    }

    if (advancedFilters.departureTime.length > 0) {
      filteredFlights = filteredFlights.filter((flight) => {
        return flight.itineraries.some((itinerary) => {
          if (itinerary.segments.length === 0) return false;

          // Get departure time of first segment
          const departureDateTime = new Date(
            itinerary.segments[0].departure.at
          );
          const departureHour = departureDateTime.getHours();

          // Check if departure time falls within any selected time range
          return advancedFilters.departureTime.some((timeRange) => {
            switch (timeRange) {
              case "morning":
                return departureHour >= 6 && departureHour < 12;
              case "afternoon":
                return departureHour >= 12 && departureHour < 18;
              case "evening":
                return departureHour >= 18 && departureHour < 24;
              case "night":
                return departureHour >= 0 && departureHour < 6;
              default:
                return false;
            }
          });
        });
      });
    }

    if (advancedFilters.arrivalTime.length > 0) {
      filteredFlights = filteredFlights.filter((flight) => {
        return flight.itineraries.some((itinerary) => {
          if (itinerary.segments.length === 0) return false;

          // Get arrival time of last segment
          const lastSegment = itinerary.segments[itinerary.segments.length - 1];
          const arrivalDateTime = new Date(lastSegment.arrival.at);
          const arrivalHour = arrivalDateTime.getHours();

          // Check if arrival time falls within any selected time range
          return advancedFilters.arrivalTime.some((timeRange) => {
            switch (timeRange) {
              case "morning":
                return arrivalHour >= 6 && arrivalHour < 12;
              case "afternoon":
                return arrivalHour >= 12 && arrivalHour < 18;
              case "evening":
                return arrivalHour >= 18 && arrivalHour < 24;
              case "night":
                return arrivalHour >= 0 && arrivalHour < 6;
              default:
                return false;
            }
          });
        });
      });
    }

    if (advancedFilters.duration.min || advancedFilters.duration.max) {
      filteredFlights = filteredFlights.filter((flight) => {
        return flight.itineraries.some((itinerary) => {
          // Calculate total duration in minutes
          const durationMinutes = itinerary.duration
            ? parseInt(itinerary.duration.replace(/PT|H|M/g, ""))
            : 0;

          // Apply min duration filter if set
          if (
            advancedFilters.duration.min &&
            durationMinutes < parseInt(advancedFilters.duration.min)
          ) {
            return false;
          }

          // Apply max duration filter if set
          if (
            advancedFilters.duration.max &&
            durationMinutes > parseInt(advancedFilters.duration.max)
          ) {
            return false;
          }

          return true;
        });
      });
    }

    return filteredFlights;
  };

  return (
    <div className="container-fluid" role="main">
      {/* Search Form */}
      {showSearchForm ? (
        <div
          className="card shadow-sm mb-3 modern-search-card"
          role="form"
          aria-label="Flight search form"
        >
          <div className="card-body p-4">
            <form onSubmit={handleSubmit} noValidate>
              {/* Trip Type Selector */}
              <div className="trip-type-selector mb-4">
                <h5 className="form-label mb-3" id="tripTypeLabel">
                  Select Trip Type
                </h5>
                <div
                  className="btn-group"
                  role="radiogroup"
                  aria-labelledby="tripTypeLabel"
                >
                  <input
                    type="radio"
                    className="btn-check"
                    name="tripType"
                    id="oneWay"
                    checked={tripType === "one-way"}
                    onChange={() => handleTripTypeChange("one-way")}
                  />
                  <label
                    className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center gap-2"
                    htmlFor="oneWay"
                  >
                    <i className="fas fa-arrow-right"></i>
                    One Way
                  </label>

                  <input
                    type="radio"
                    className="btn-check"
                    name="tripType"
                    id="roundTrip"
                    checked={tripType === "round"}
                    onChange={() => handleTripTypeChange("round")}
                  />
                  <label
                    className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center gap-2"
                    htmlFor="roundTrip"
                  >
                    <i className="fas fa-exchange-alt"></i>
                    Round Trip
                  </label>

                  <input
                    type="radio"
                    className="btn-check"
                    name="tripType"
                    id="multiCity"
                    checked={tripType === "multi-city"}
                    onChange={() => handleTripTypeChange("multi-city")}
                  />
                  <label
                    className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center gap-2"
                    htmlFor="multiCity"
                  >
                    <i className="fas fa-route"></i>
                    Multi-City
                  </label>
                </div>
              </div>

              {tripType !== "multi-city" ? (
                <div className="search-fields-container mb-4">
                  <div className="row g-3">
                    <div className="col-md-3 d-flex flex-column">
                      <label className="form-label fw-bold" id="originLabel">
                        Origin (IATA Code)
                        <span className="visually-hidden">
                          Select departure city or airport
                        </span>
                      </label>
                      <div className="flex-grow-1">
                        <AirportAutocomplete
                          name="origin"
                          value={formData.origin}
                          onChange={handleInputChange}
                          placeholder="From"
                          isInvalid={!!formErrors.origin}
                          errorMessage={formErrors.origin}
                          aria-labelledby="originLabel"
                          aria-invalid={!!formErrors.origin}
                          aria-describedby={
                            formErrors.origin ? "originError" : undefined
                          }
                        />
                        {formErrors.origin && (
                          <div
                            id="originError"
                            className="invalid-feedback d-block"
                          >
                            {formErrors.origin}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="col-md-auto d-flex align-items-center px-2">
                      <button
                        type="button"
                        className="btn btn-outline-secondary rounded-circle p-2 swap-button"
                        onClick={handleSwapLocations}
                        aria-label="Swap origin and destination"
                      >
                        <i className="fas fa-exchange-alt"></i>
                      </button>
                    </div>

                    <div className="col-md-3 d-flex flex-column">
                      <label
                        className="form-label fw-bold"
                        id="destinationLabel"
                      >
                        Destination (IATA Code)
                        <span className="visually-hidden">
                          Select arrival city or airport
                        </span>
                      </label>
                      <div className="flex-grow-1">
                        <AirportAutocomplete
                          name="destination"
                          value={formData.destination}
                          onChange={handleInputChange}
                          placeholder="To"
                          isInvalid={!!formErrors.destination}
                          errorMessage={formErrors.destination}
                          aria-labelledby="destinationLabel"
                          aria-invalid={!!formErrors.destination}
                          aria-describedby={
                            formErrors.destination
                              ? "destinationError"
                              : undefined
                          }
                        />
                        {formErrors.destination && (
                          <div
                            id="destinationError"
                            className="invalid-feedback d-block"
                          >
                            {formErrors.destination}
                          </div>
                        )}
                      </div>
                    </div>

                    {tripType === "round" ? (
                      <div className="col-md d-flex flex-column">
                        <DateRangePicker
                          startDateName="departure"
                          endDateName="returnDate"
                          startDate={formData.departure}
                          endDate={formData.returnDate}
                          onChange={handleInputChange}
                          startDateLabel="Departure Date*"
                          endDateLabel="Return Date*"
                          minDate={new Date()}
                          isStartDateInvalid={!!formErrors.departure}
                          isEndDateInvalid={!!formErrors.returnDate}
                          startDateErrorMessage={formErrors.departure}
                          endDateErrorMessage={formErrors.returnDate}
                          required={true}
                        />
                      </div>
                    ) : (
                      <div className="col-md-3 d-flex flex-column">
                        <DatePicker
                          name="departure"
                          selected={formData.departure}
                          onChange={handleInputChange}
                          label="Departure Date*"
                          minDate={new Date()}
                          isInvalid={!!formErrors.departure}
                          errorMessage={formErrors.departure}
                          placeholder="Select date"
                          required={true}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <MultiCityInput
                  segments={multiCitySegments}
                  onChange={handleMultiCityChange}
                  onAddSegment={handleAddSegment}
                  onRemoveSegment={handleRemoveSegment}
                  errors={multiCityErrors}
                />
              )}

              {/* Passengers and Class */}
              <div
                className="passenger-section mb-4"
                role="group"
                aria-labelledby="passengerSectionLabel"
              >
                <h5 className="form-label mb-3" id="passengerSectionLabel">
                  Passengers & Cabin
                </h5>
                <div className="d-flex flex-wrap gap-3">
                  <div className="passenger-type-card flex-grow-1">
                    <div className="passenger-type-header">
                      <i className="fas fa-users"></i>
                      <span>Travelers</span>
                    </div>
                    <div className="passenger-type-controls">
                      <div className="dropdown">
                        <button
                          className="btn btn-outline-secondary dropdown-toggle d-inline-block w-auto p-2"
                          type="button"
                          id="passengerDropdown"
                          data-bs-toggle="dropdown"
                          data-bs-auto-close="outside"
                          aria-expanded="false"
                        >
                          <i className="fas fa-users me-2"></i>
                          {formData.passengers +
                            formData.children +
                            formData.infants}{" "}
                          Travelers
                        </button>
                        <ul
                          className="dropdown-menu"
                          aria-labelledby="passengerDropdown"
                        >
                          <li>
                            <div className="dropdown-item">
                              Adults (12+ years)
                              <div className="input-group">
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      passengers: Math.max(
                                        1,
                                        formData.passengers - 1
                                      ),
                                    });
                                  }}
                                  disabled={formData.passengers <= 1}
                                >
                                  <i className="fas fa-minus"></i>
                                </button>
                                <input
                                  type="text"
                                  className="form-control text-center"
                                  value={formData.passengers}
                                  readOnly
                                />
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      passengers: Math.min(
                                        9,
                                        formData.passengers + 1
                                      ),
                                    });
                                  }}
                                  disabled={formData.passengers >= 9}
                                >
                                  <i className="fas fa-plus"></i>
                                </button>
                              </div>
                            </div>
                          </li>
                          <li>
                            <div className="dropdown-item">
                              Children (2-11 years)
                              <div className="input-group">
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      children: Math.max(
                                        0,
                                        formData.children - 1
                                      ),
                                    });
                                  }}
                                  disabled={formData.children <= 0}
                                >
                                  <i className="fas fa-minus"></i>
                                </button>
                                <input
                                  type="text"
                                  className="form-control text-center"
                                  value={formData.children}
                                  readOnly
                                />
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      children: Math.min(
                                        9,
                                        formData.children + 1
                                      ),
                                    });
                                  }}
                                  disabled={formData.children >= 9}
                                >
                                  <i className="fas fa-plus"></i>
                                </button>
                              </div>
                            </div>
                          </li>
                          <li>
                            <div className="dropdown-item">
                              Infants (Under 2 years)
                              <div className="input-group">
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      infants: Math.max(
                                        0,
                                        formData.infants - 1
                                      ),
                                    });
                                  }}
                                  disabled={formData.infants <= 0}
                                >
                                  <i className="fas fa-minus"></i>
                                </button>
                                <input
                                  type="text"
                                  className="form-control text-center"
                                  value={formData.infants}
                                  readOnly
                                />
                                <button
                                  type="button"
                                  className="btn btn-outline-secondary"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setFormData({
                                      ...formData,
                                      infants: Math.min(
                                        formData.passengers,
                                        formData.infants + 1
                                      ),
                                    });
                                  }}
                                  disabled={
                                    formData.infants >= formData.passengers
                                  }
                                >
                                  <i className="fas fa-plus"></i>
                                </button>
                              </div>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="passenger-type-card flex-grow-1">
                    <div className="passenger-type-header">
                      <i className="fas fa-couch"></i>
                      <span>Cabin Class</span>
                    </div>
                    <div className="passenger-type-controls">
                      <select
                        name="cabinClass"
                        className="form-select"
                        value={formData.cabinClass}
                        onChange={handleInputChange}
                      >
                        <option value="ECONOMY">Economy</option>
                        <option value="PREMIUM_ECONOMY">Premium Economy</option>
                        <option value="BUSINESS">Business</option>
                        <option value="FIRST">First Class</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-secondary mb-4">
                {/* Flexible Date Search */}
                <FlexibleDateSearch
                  onFlexibleDateChange={handleFlexibleDateChange}
                  searchParams={{
                    originLocationCode: formData.origin,
                    destinationLocationCode: formData.destination,
                    departureDate: formData.departure,
                    adults: formData.passengers,
                    children: formData.children,
                    infants: formData.infants,
                    travelClass: formData.cabinClass,
                  }}
                />
              </div>

              {/* Submit Button */}
              <div className="search-button-container ">
                <div className="d-grid">
                  <button
                    type="submit"
                    className="btn btn-info btn-lg search-button d-flex justify-content-center align-items-center gap-2"
                    disabled={loading}
                    aria-busy={loading}
                  >
                    {loading ? (
                      <>
                        <span
                          className="spinner-border spinner-border-sm me-2"
                          role="status"
                          aria-hidden="true"
                        ></span>
                        <span>Searching Flights...</span>
                      </>
                    ) : (
                      <>
                        <span className="d-flex align-items-center gap-2">
                          <i className="fas fa-search" aria-hidden="true"></i>
                          <span>Search Flights</span>
                          <i
                            className="fas fa-arrow-right"
                            aria-hidden="true"
                          ></i>
                        </span>
                      </>
                    )}
                  </button>
                </div>
                <p className="text-center text-muted mt-2 mb-0">
                  <small>
                    <i className="fas fa-shield-alt me-1"></i>
                    Secure search • Best prices guaranteed
                  </small>
                </p>
              </div>

              {error && (
                <div className="alert alert-danger mt-3" role="alert">
                  <i
                    className="fas fa-exclamation-circle me-2"
                    aria-hidden="true"
                  ></i>
                  {error}
                </div>
              )}
            </form>
          </div>
        </div>
      ) : (
        <div className="modify-search-container">
          <div className="card shadow-sm">
            <div className="card-body text-center py-3">
              <div className="d-flex align-items-center justify-content-center gap-3">
                <div className="search-summary">
                  <small className="text-muted">
                    <i className="fas fa-route me-1"></i>
                    {formData.origin} → {formData.destination}
                    {tripType === "round" && ` → ${formData.origin}`}
                  </small>
                  <br />
                  <small className="text-muted">
                    <i className="fas fa-calendar me-1"></i>
                    {formData.departure}
                    {tripType === "round" &&
                      formData.returnDate &&
                      ` - ${formData.returnDate}`}
                  </small>
                </div>
                <button
                  className="btn btn-secondary modify-btn"
                  onClick={() => setShowSearchForm(true)}
                >
                  <i className="fas fa-edit me-2"></i>
                  Modify Search
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* ================== */}
      {/* Results and Filters Section */}
      {(flights.length > 0 || returnFlights.length > 0) && (
        <div className="container mt-5" ref={resultsRef}>
          <div className="row">
            {/* Advanced Filters Sidebar */}
            <div className="col-md-3">
              <FilterPanel
                onFilterChange={handleAdvancedFilterChange}
                airlines={availableAirlines}
              />
            </div>

            {/* Main Results Area */}
            <div className="col-md-9">
              {/* Flight Results */}
              {flights.length > 0 && (
                <div className="container">
                  <div className="row">
                    {/* Leave space for the sidebar on the left */}

                    {/* Flight results on the right */}
                    <div className="">
                      {/* Outbound Flights */}
                      <div className="card shadow-sm mb-4">
                        <div className="card-body">
                          <h4 className="mb-0 ">
                            <i className="fas fa-arrow-right me-2"></i>{" "}
                            {tripType === "multi-city"
                              ? "Multi-City Flights"
                              : "Outbound Flight"}
                          </h4>
                        </div>
                        <div className="row p-3 alert-primary text-center">
                          <div className="col-md-2 fw-bold">Airline</div>
                          <div className="col-md-2 fw-bold">Flight No</div>
                          <div className="col-md-2 fw-bold">Departure</div>
                          <div className="col-md-2 fw-bold">Duration</div>
                          <div className="col-md-2 fw-bold">Arrival</div>
                          <div className="col-md-2 fw-bold">Price</div>
                        </div>
                        <div className="card-body p-0">
                          <FlightResult
                            flights={handleSortAndFilter(flights)}
                            onFlightSelect={handleFlightSelect}
                            selectedFlight={selectedFlights.outbound}
                          />
                        </div>
                      </div>

                      {/* Return flights section */}
                      {returnFlights.length > 0 && (
                        <div className="card shadow-sm mt-3">
                          <div className="card-body">
                            <h4 className="mb-0">
                              <i className="fas fa-arrow-left me-2"></i> Return
                              Flight
                            </h4>
                          </div>
                          <div className="row p-3 alert-primary text-center">
                            <div className="col-md-2 fw-bold">Airline</div>
                            <div className="col-md-2 fw-bold">Flight No</div>
                            <div className="col-md-2 fw-bold">Departure</div>
                            <div className="col-md-2 fw-bold">Duration</div>
                            <div className="col-md-2 fw-bold">Arrival</div>
                            <div className="col-md-2 fw-bold">Price</div>
                          </div>
                          <div className="card-body p-0">
                            <ReturnFlightResult
                              flights={handleSortAndFilter(returnFlights)}
                              onFlightSelect={(flight) =>
                                handleFlightSelect(flight, "return")
                              }
                              selectedFlight={selectedFlights.return}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* ===================  */}

      {/* Modal */}
      {showPricingModal && (
        <Modal show={showPricingModal} onHide={handleClosePricing} size="lg">
          <Modal.Body>
            <PricingSelect
              selectedFlights={selectedFlights}
              onClose={handleClosePricing}
              passengers={formData.passengers}
              children={formData.children}
              infants={formData.infants}
            />
          </Modal.Body>
        </Modal>
      )}
    </div>
  );
};

export default FlightSearch;
