import React from "react";
import DatePicker from "./DatePicker";
import "react-datepicker/dist/react-datepicker.css";
import "../assets/styles/DatePicker.css";

const PassengerForm = ({ passenger, index, handlePassengerChange, errors }) => {
  // Add passenger validation schema
  const validatePassengerDetails = (passenger) => {
    const errors = {};
    
    // Basic validation
    if (!passenger.firstName) errors.firstName = "First name is required";
    if (!passenger.lastName) errors.lastName = "Last name is required";
    if (!passenger.dob) errors.dob = "Date of birth is required";
    
    // Passport validation for international flights
    if (isInternationalFlight) {
      if (!passenger.passportNumber) {
        errors.passportNumber = "Passport number is required for international flights";
      }
      if (!passenger.passportExpiry) {
        errors.passportExpiry = "Passport expiry date is required";
      } else {
        const expiryDate = new Date(passenger.passportExpiry);
        const sixMonthsFromNow = new Date();
        sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);
        
        if (expiryDate < sixMonthsFromNow) {
          errors.passportExpiry = "Passport must be valid for at least 6 months";
        }
      }
    }
    
    // Special assistance validation
    if (passenger.specialAssistance) {
      if (!passenger.specialAssistanceDetails) {
        errors.specialAssistanceDetails = "Please provide details for special assistance";
      }
    }
    
    // Infant validation
    if (passenger.type === 'INFANT') {
      const dobDate = new Date(passenger.dob);
      const today = new Date();
      const ageInMonths = (today.getFullYear() - dobDate.getFullYear()) * 12 + 
                         today.getMonth() - dobDate.getMonth();
      
      if (ageInMonths > 24) {
        errors.dob = "Infant must be under 2 years old";
      }
    }
    
    return errors;
  };

  return (
    <div className="passenger-form-fields">
      {/* Personal Information Section */}
      <div className="card mb-4">
        <div className="card-header">Personal Information</div>
        <div className="card-body">
          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor={`firstName-${index}`} className="form-label">
                First Name
              </label>
              <input
                type="text"
                className={`form-control ${errors?.firstName ? "is-invalid" : ""}`}
                id={`firstName-${index}`}
                value={passenger.firstName || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "firstName", e.target.value)
                }
                required
                maxLength={50}
                pattern="[A-Za-z\s-']*"
              />
              {errors?.firstName && (
                <div className="invalid-feedback">{errors.firstName}</div>
              )}
            </div>

            <div className="col-md-6">
              <label htmlFor={`lastName-${index}`} className="form-label">
                Last Name
              </label>
              <input
                type="text"
                className={`form-control ${errors?.lastName ? "is-invalid" : ""}`}
                id={`lastName-${index}`}
                value={passenger.lastName || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "lastName", e.target.value)
                }
                required
                maxLength={50}
                pattern="[A-Za-z\s-']*"
              />
              {errors?.lastName && (
                <div className="invalid-feedback">{errors.lastName}</div>
              )}
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor={`passport-${index}`} className="form-label">
                Passport Number
                {passenger.type === "INFANT" && " (Optional)"}
              </label>
              <input
                type="text"
                className={`form-control ${errors?.passport ? "is-invalid" : ""}`}
                id={`passport-${index}`}
                value={passenger.passport || ""}
                onChange={(e) =>
                  handlePassengerChange(
                    index,
                    "passport",
                    e.target.value.toUpperCase()
                  )
                }
                required={passenger.type !== "INFANT"}
                maxLength={9}
                pattern="[A-Z0-9]*"
              />
              {errors?.passport && (
                <div className="invalid-feedback">{errors.passport}</div>
              )}
            </div>

            {passenger.type !== "INFANT" && (
              <div className="col-md-6">
                <DatePicker
                  name={`passportExpiry-${index}`}
                  label="Passport Expiry Date"
                  selected={passenger.passportExpiry ? new Date(passenger.passportExpiry) : null}
                  onChange={(e) => handlePassengerChange(index, "passportExpiry", e.target.value)}
                  minDate={new Date()}
                  isInvalid={!!errors?.passportExpiry}
                  errorMessage={errors?.passportExpiry}
                  placeholder="Select passport expiry date"
                  required={passenger.type !== "INFANT"}
                />
              </div>
            )}
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <DatePicker
                name={`dob-${index}`}
                label="Date of Birth"
                selected={passenger.dob ? new Date(passenger.dob) : null}
                onChange={(e) => handlePassengerChange(index, "dob", e.target.value)}
                maxDate={new Date()}
                isInvalid={!!errors?.dob}
                errorMessage={errors?.dob}
                placeholder="Select date of birth"
                required
              />
            </div>

            <div className="col-md-6">
              <label htmlFor={`gender-${index}`} className="form-label">
                Gender
              </label>
              <select
                className={`form-select ${errors?.gender ? "is-invalid" : ""}`}
                id={`gender-${index}`}
                value={passenger.gender || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "gender", e.target.value)
                }
                required
              >
                <option value="">Select Gender</option>
                <option value="MALE">Male</option>
                <option value="FEMALE">Female</option>
                <option value="OTHER">Other</option>
                <option value="PREFER_NOT_TO_SAY">Prefer not to say</option>
              </select>
              {errors?.gender && (
                <div className="invalid-feedback">{errors.gender}</div>
              )}
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor={`email-${index}`} className="form-label">
                Email
              </label>
              <input
                type="email"
                className={`form-control ${errors?.email ? "is-invalid" : ""}`}
                id={`email-${index}`}
                value={passenger.email || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "email", e.target.value)
                }
                required
              />
              {errors?.email && (
                <div className="invalid-feedback">{errors.email}</div>
              )}
            </div>

            <div className="col-md-6">
              <label htmlFor={`phone-${index}`} className="form-label">
                Phone Number (E.164 format)
              </label>
              <input
                type="tel"
                className={`form-control ${errors?.phone ? "is-invalid" : ""}`}
                id={`phone-${index}`}
                value={passenger.phone || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "phone", e.target.value)
                }
                required
                placeholder="+1234567890"
              />
              {errors?.phone && (
                <div className="invalid-feedback">{errors.phone}</div>
              )}
              <small className="form-text text-muted">
                Enter phone number in international format (e.g., +1234567890)
              </small>
            </div>
          </div>
        </div>
      </div>

      {/* Special Requirements Section */}
      <div className="card mb-4">
        <div className="card-header">Special Requirements</div>
        <div className="card-body">
          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor={`mealPreference-${index}`} className="form-label">
                Meal Preferences
              </label>
              <select
                className={`form-select ${errors?.mealPreference ? "is-invalid" : ""}`}
                id={`mealPreference-${index}`}
                value={passenger.mealPreference || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "mealPreference", e.target.value)
                }
              >
                <option value="">Select Meal Preference</option>
                <option value="REGULAR">Regular</option>
                <option value="VEGETARIAN">Vegetarian</option>
                <option value="VEGAN">Vegan</option>
                <option value="HALAL">Halal</option>
                <option value="KOSHER">Kosher</option>
                <option value="GLUTEN_FREE">Gluten Free</option>
                <option value="DIABETIC">Diabetic</option>
              </select>
              {errors?.mealPreference && (
                <div className="invalid-feedback">{errors.mealPreference}</div>
              )}
            </div>

            <div className="col-md-6">
              <label htmlFor={`wheelchair-${index}`} className="form-label">
                Wheelchair Assistance
              </label>
              <select
                className={`form-select ${errors?.wheelchair ? "is-invalid" : ""}`}
                id={`wheelchair-${index}`}
                value={passenger.wheelchair || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "wheelchair", e.target.value)
                }
              >
                <option value="">Select Option</option>
                <option value="NONE">No assistance needed</option>
                <option value="WCHR">Can climb stairs (WCHR)</option>
                <option value="WCHS">Cannot climb stairs (WCHS)</option>
                <option value="WCHC">Cannot walk (WCHC)</option>
              </select>
              {errors?.wheelchair && (
                <div className="invalid-feedback">{errors.wheelchair}</div>
              )}
            </div>
          </div>

          <div className="row mb-3">
            <div className="col-md-12">
              <label htmlFor={`specialNeeds-${index}`} className="form-label">
                Other Special Needs
              </label>
              <textarea
                className={`form-control ${errors?.specialNeeds ? "is-invalid" : ""}`}
                id={`specialNeeds-${index}`}
                value={passenger.specialNeeds || ""}
                onChange={(e) =>
                  handlePassengerChange(index, "specialNeeds", e.target.value)
                }
                rows="3"
                maxLength={500}
                placeholder="Enter any other special needs or requirements"
              ></textarea>
              {errors?.specialNeeds && (
                <div className="invalid-feedback">{errors.specialNeeds}</div>
              )}
              <small className="form-text text-muted">
                Maximum 500 characters
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PassengerForm;
