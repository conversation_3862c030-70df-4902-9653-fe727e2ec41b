import React from "react";
import FlightItem from "./FlightItem";
import "../assets/styles/flightSearch.css";

/**
 * Component for displaying return flight search results
 */
function ReturnFlightResult({ flights = [], onFlightSelect, selectedFlight }) {
  return (
    <div>
      {flights.length === 0 ? (
        <div className="alert alert-info">No return flights found matching your criteria.</div>
      ) : (
        flights.map((flight) => (
          <FlightItem
            key={flight.id}
            flight={flight}
            onSelect={(flight) => onFlightSelect(flight, "return")}
            isSelected={selectedFlight?.id === flight.id}
            buttonText="Select Return"
            selectedButtonText="Return Selected"
          />
        ))
      )}
    </div>
  );
}

export default ReturnFlightResult;
