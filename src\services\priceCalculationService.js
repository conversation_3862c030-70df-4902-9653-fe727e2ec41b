/**
 * Centralized service for handling all price calculations in the application
 */
export class PriceCalculationService {
  static FARE_MULTIPLIERS = {
    basic: 1.0,
    standard: 1.1, // 10% more than basic
    premium: 1.25, // 25% more than basic
    flex: 1.2, // 20% more than basic
  };

  static PASSENGER_TYPE_MULTIPLIERS = {
    adult: 1.0,
    child: 0.75, // 75% of adult price
    infant: 0.1, // 10% of adult price
  };

  static SEAT_PRICES = {
    premium: 1000, // Front rows (1-5)
    exit: 800, // Exit rows (12-14)
    standard: 500, // Regular seats
    window: 200, // Additional cost for window seats
  };

  static SEAT_DISCOUNTS = {
    basic: 1.0, // No discount
    standard: 0.95, // 5% discount
    premium: 0.8, // 20% discount
    flex: 0.9, // 10% discount
  };

  // Baggage pricing
  static BAGGAGE_PRICES = {
    none: 0,
    carry: 0, // Carry-on is free
    checked15: 1500,
    checked20: 2000,
    checked25: 2500,
    checked30: 3000,
    excess: 4000,
  };

  // Current promotional codes and their discounts
  static promotionalDiscounts = {
    WELCOME2025: { type: "percentage", value: 10 }, // 10% off
    FAMILYTRIP: { type: "fixed", value: 1000 }, // Fixed amount off
    SUMMERFUN: { type: "percentage", value: 15 }, // 15% off
    EARLYBIRD: { type: "percentage", value: 20, expiryDate: "2025-12-31" }, // 20% off until end of 2025
  };

  /**
   * Calculate base ticket price including return flight if applicable
   */
  static calculateBasePrice(flights) {
    if (!flights?.outbound) return 0;

    const outboundPrice = parseFloat(
      String(flights.outbound?.price?.total || 0)
    );
    const returnPrice = parseFloat(String(flights.return?.price?.total || 0));

    return isNaN(outboundPrice)
      ? 0
      : outboundPrice + (isNaN(returnPrice) ? 0 : returnPrice);
  }

  /**
   * Calculate total price for passengers
   */
  static calculatePassengerTypePrice(basePrice, type, count) {
    if (!basePrice || !type || !count) return 0;
    const multiplier = this.PASSENGER_TYPE_MULTIPLIERS[type.toLowerCase()] || 1;
    return basePrice * multiplier * Number(count);
  }

  /**
   * Calculate seat price based on location and pricing option
   */
  static calculateSeatPrice(row, seatNumber, pricingOption) {
    let basePrice = this.SEAT_PRICES.standard;

    // Determine seat type price
    if (row <= 5) {
      basePrice = this.SEAT_PRICES.premium;
    } else if (row >= 12 && row <= 14) {
      basePrice = this.SEAT_PRICES.exit;
    }

    // Add window seat premium
    if ([0, 5, 6, 11].includes(seatNumber)) {
      basePrice += this.SEAT_PRICES.window;
    }

    // Apply pricing option discount
    const discount = this.SEAT_DISCOUNTS[pricingOption.toLowerCase()] || 1;
    return basePrice * discount;
  }

  /**
   * Validate and calculate promotional discount
   */
  static calculatePromotionalDiscount(total, promoCode) {
    if (!promoCode || !this.promotionalDiscounts[promoCode]) {
      return {
        discountAmount: 0,
        isValid: false,
        message: "Invalid promotional code",
      };
    }

    const discount = this.promotionalDiscounts[promoCode];

    // Check expiry date if present
    if (discount.expiryDate && new Date() > new Date(discount.expiryDate)) {
      return {
        discountAmount: 0,
        isValid: false,
        message: "Promotional code has expired",
      };
    }

    let discountAmount = 0;
    if (discount.type === "percentage") {
      discountAmount = (total * discount.value) / 100;
    } else if (discount.type === "fixed") {
      discountAmount = Math.min(discount.value, total); // Don't exceed total amount
    }

    return {
      discountAmount,
      isValid: true,
      message: `Discount applied: ${
        discount.type === "percentage"
          ? discount.value + "%"
          : "BDT " + discount.value
      }`,
    };
  }

  /**
   * Calculate baggage cost
   */
  static calculateBaggageCost(baggageSelections) {
    if (!baggageSelections) return 0;

    let total = 0;

    // Calculate outbound baggage cost
    if (baggageSelections.outbound) {
      Object.values(baggageSelections.outbound).forEach((baggageType) => {
        total += this.BAGGAGE_PRICES[baggageType] || 0;
      });
    }

    // Calculate return baggage cost
    if (baggageSelections.return) {
      Object.values(baggageSelections.return).forEach((baggageType) => {
        total += this.BAGGAGE_PRICES[baggageType] || 0;
      });
    }

    return total;
  }

  /**
   * Calculate total price for booking
   */
  static calculateTotalPrice({
    flights,
    passengers = { adult: 0, child: 0, infant: 0 },
    pricingOption = "basic",
    seatSelections = null,
    baggageSelections = null,
    promoCode,
  }) {
    // Calculate base ticket prices
    const basePrice = this.calculateBasePrice(flights);
    const fareMultiplier =
      this.FARE_MULTIPLIERS[pricingOption.toLowerCase()] || 1;
    const priceWithFare = basePrice * fareMultiplier;

    // Calculate passenger type totals
    const passengerTotal = Object.entries(passengers).reduce(
      (total, [type, count]) => {
        return (
          total + this.calculatePassengerTypePrice(priceWithFare, type, count)
        );
      },
      0
    );

    // Calculate seat costs if any
    let seatTotal = 0;
    if (seatSelections) {
      const calculateFlightSeats = (flightSeats) => {
        return Object.entries(flightSeats || {}).reduce(
          (total, [seatNumber, _]) => {
            const matches = seatNumber.match(/(\d+)([A-Z])/);
            if (!matches) return total;
            const row = parseInt(matches[1]);
            const seatLetter = matches[2];
            const seatIndex = seatLetter.charCodeAt(0) - 65;
            return (
              total + this.calculateSeatPrice(row, seatIndex, pricingOption)
            );
          },
          0
        );
      };

      seatTotal += calculateFlightSeats(seatSelections.outbound);
      seatTotal += calculateFlightSeats(seatSelections.return);
    }

    // Calculate baggage costs
    const baggageTotal = this.calculateBaggageCost(baggageSelections);

    // Calculate subtotal before promotional discount
    const subtotal = passengerTotal + seatTotal + baggageTotal;

    // Apply promotional discount if provided
    const { discountAmount, isValid, message } =
      this.calculatePromotionalDiscount(subtotal, promoCode);

    // Currency from flight data or default to BDT
    const currency = flights?.outbound?.price?.currency || "BDT";

    return {
      basePrice,
      fareMultiplier,
      passengerTotal,
      seatTotal,
      baggageTotal,
      subtotal,
      promoDiscount: discountAmount,
      promoMessage: message,
      total: subtotal - discountAmount,
      currency,
      isPromoValid: isValid,
    };
  }
}
