import React, { useState, useEffect } from "react";
import { useAppContext } from "../../context/AppContext";
import DatePicker from "../../Components/DatePicker";
import PassengerForm from "../../Components/PassengerForm";
import { validatePassenger } from "../../utils/passengerValidation";
import "../../assets/styles/Booking.css";

const AUTO_SAVE_DELAY = 1000; // Auto-save delay in milliseconds

/**
 * Component for collecting passenger details during booking
 */
const PassengerDetails = ({
  adults,
  children,
  infants,
  onComplete,
  onSkipToPayment,
}) => {
  const { state, dispatch } = useAppContext();
  const formId = "passenger-details";

  // Create an array of passengers with their types
  const createPassengersArray = () => {
    // Try to recover saved form data first
    const savedData = state.formData[formId];
    if (savedData) {
      return savedData;
    }

    const passengersArray = [];
    const basePassenger = {
      firstName: "",
      lastName: "",
      passport: "",
      passportExpiry: "",
      dob: "",
      email: "",
      phone: "",
      gender: "",
      mealPreference: "",
      wheelchair: "NONE",
      specialNeeds: "",
      errors: {},
    };

    // Add adult passengers
    for (let i = 0; i < adults; i++) {
      passengersArray.push({
        ...basePassenger,
        type: "ADULT",
      });
    }

    // Add child passengers
    for (let i = 0; i < children; i++) {
      passengersArray.push({
        ...basePassenger,
        type: "CHILD",
      });
    }

    // Add infant passengers with adult association
    for (let i = 0; i < infants; i++) {
      passengersArray.push({
        ...basePassenger,
        type: "INFANT",
        associatedAdult: i < adults ? i + 1 : 1,
      });
    }

    return passengersArray;
  };

  const [passengers, setPassengers] = useState(createPassengersArray());
  const [lastSaved, setLastSaved] = useState(null);

  // Auto-save effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      dispatch({
        type: "SAVE_FORM_DATA",
        payload: {
          formId,
          data: passengers,
        },
      });
      setLastSaved(new Date());
    }, AUTO_SAVE_DELAY);

    return () => clearTimeout(timeoutId);
  }, [passengers, dispatch]);

  // Clean up form data when component unmounts
  useEffect(() => {
    return () => {
      dispatch({
        type: "CLEAR_FORM_DATA",
        payload: formId,
      });
    };
  }, [dispatch]);

  // Removed local validatePassenger function

  const handlePassengerChange = (index, field, value) => {
    const updatedPassengers = [...passengers];
    updatedPassengers[index][field] = value;

    // Clear error when field is edited
    if (updatedPassengers[index].errors?.[field]) {
      updatedPassengers[index].errors[field] = "";
    }

    setPassengers(updatedPassengers);
  };

  // Enhanced handleSubmit with additional validation logic
  const handleSubmit = (e) => {
    e.preventDefault();
    let isValid = true;
    const validatedPassengers = passengers.map((passenger) => {
      const errors = validatePassenger(passenger, passengers);

      // Additional cross-validation for infants and adults
      if (passenger.type === "INFANT") {
        const associatedAdult = passengers.find(
          (p, idx) =>
            p.type === "ADULT" && idx + 1 === passenger.associatedAdult
        );

        if (!associatedAdult) {
          errors.associatedAdult = "Please select a valid accompanying adult";
          isValid = false;
        } else {
          // Verify adult's details are complete
          const adultErrors = validatePassenger(associatedAdult, passengers);
          if (Object.keys(adultErrors).length > 0) {
            errors.associatedAdult =
              "Please complete accompanying adult's details first";
            isValid = false;
          }
        }
      }

      if (Object.keys(errors).length > 0) {
        isValid = false;
      }
      return { ...passenger, errors };
    });

    setPassengers(validatedPassengers);

    if (isValid) {
      // Clear saved form data on successful submission
      dispatch({
        type: "CLEAR_FORM_DATA",
        payload: formId,
      });

      // Format data for completion
      const formattedPassengers = validatedPassengers.map(
        ({ errors, ...rest }) => ({
          ...rest,
          dob: new Date(rest.dob).toISOString(),
          passportExpiry: rest.passportExpiry
            ? new Date(rest.passportExpiry).toISOString()
            : undefined,
        })
      );

      onComplete(formattedPassengers);
    } else {
      // Scroll to first error
      const firstErrorElement = document.querySelector(".is-invalid");
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  };

  const handleSkipToPayment = (e) => {
    e.preventDefault();
    let isValid = true;
    const validatedPassengers = passengers.map((passenger) => {
      const errors = validatePassenger(passenger, passengers);

      // Additional cross-validation for infants and adults
      if (passenger.type === "INFANT") {
        const associatedAdult = passengers.find(
          (p, idx) =>
            p.type === "ADULT" && idx + 1 === passenger.associatedAdult
        );

        if (!associatedAdult) {
          errors.associatedAdult = "Please select a valid accompanying adult";
          isValid = false;
        } else {
          // Verify adult's details are complete
          const adultErrors = validatePassenger(associatedAdult, passengers);
          if (Object.keys(adultErrors).length > 0) {
            errors.associatedAdult =
              "Please complete accompanying adult's details first";
            isValid = false;
          }
        }
      }

      if (Object.keys(errors).length > 0) {
        isValid = false;
      }
      return { ...passenger, errors };
    });

    setPassengers(validatedPassengers);

    if (isValid) {
      // Clear saved form data on successful submission
      dispatch({
        type: "CLEAR_FORM_DATA",
        payload: formId,
      });

      // Format data for completion
      const formattedPassengers = validatedPassengers.map(
        ({ errors, ...rest }) => ({
          ...rest,
          dob: new Date(rest.dob).toISOString(),
          passportExpiry: rest.passportExpiry
            ? new Date(rest.passportExpiry).toISOString()
            : undefined,
        })
      );

      if (onSkipToPayment) {
        onSkipToPayment(formattedPassengers);
      }
    } else {
      // Scroll to first error
      const firstErrorElement = document.querySelector(".is-invalid");
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  };

  return (
    <div className="passenger-details-container">
      <h3>Passenger Details</h3>
      <p className="text-muted mb-4">
        Please enter the details for all passengers as they appear on their
        official travel documents.
      </p>

      {/* Auto-save indicator */}
      {lastSaved && (
        <div className="auto-save-indicator">
          <small className="text-muted">
            <i className="fas fa-save me-1"></i>
            Last saved: {lastSaved.toLocaleTimeString()}
          </small>
        </div>
      )}

      <form className="passenger-form" onSubmit={handleSubmit}>
        {passengers.map((passenger, index) => (
          <div
            key={index}
            className={`passenger-card ${
              Object.keys(passenger.errors || {}).length > 0 ? "has-errors" : ""
            }`}
          >
            <div className="card-header d-flex justify-content-between align-items-center">
              <h4>
                {passenger.type}{" "}
                {passenger.type === "INFANT" &&
                  (() => {
                    if (!passenger.dob) return "";
                    const dobDate = new Date(passenger.dob);
                    if (isNaN(dobDate.getTime())) return "";
                    const today = new Date();
                    const ageInMonths = Math.floor(
                      (today - dobDate) / (1000 * 60 * 60 * 24 * 30.44)
                    );
                    return ageInMonths < 24
                      ? ` (${ageInMonths} months old)`
                      : "";
                  })()}
              </h4>
              <span className="passenger-number">Passenger {index + 1}</span>
            </div>

            <div className="card-body">
              <PassengerForm
                passenger={passenger}
                index={index}
                handlePassengerChange={handlePassengerChange}
                errors={passenger.errors}
              />

              {/* Infant-Adult Association */}
              {passenger.type === "INFANT" && (
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label className="form-label">Traveling with Adult</label>
                    <select
                      className={`form-select ${
                        passenger.errors?.associatedAdult ? "is-invalid" : ""
                      }`}
                      value={passenger.associatedAdult || ""}
                      onChange={(e) =>
                        handlePassengerChange(
                          index,
                          "associatedAdult",
                          parseInt(e.target.value)
                        )
                      }
                      required
                    >
                      <option value="">Select accompanying adult</option>
                      {passengers
                        .filter((p) => p.type === "ADULT")
                        .map((adult, adultIndex) => (
                          <option
                            key={adultIndex}
                            value={adultIndex + 1}
                            disabled={passengers.some(
                              (p) =>
                                p !== passenger &&
                                p.type === "INFANT" &&
                                p.associatedAdult === adultIndex + 1
                            )}
                          >
                            Adult {adultIndex + 1}
                            {adult.firstName
                              ? ` - ${adult.firstName} ${adult.lastName}`
                              : ""}
                            {passengers.some(
                              (p) =>
                                p !== passenger &&
                                p.type === "INFANT" &&
                                p.associatedAdult === adultIndex + 1
                            )
                              ? " (Already has an infant)"
                              : ""}
                          </option>
                        ))}
                    </select>
                    {passenger.errors?.associatedAdult && (
                      <div className="invalid-feedback">
                        {passenger.errors.associatedAdult}
                      </div>
                    )}
                    <small className="form-text text-muted">
                      Each infant must be associated with an adult passenger
                    </small>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}

        <div className="row gap-2 mt-4">
          <div className="col-md-6">
            <button type="submit" className="btn btn-primary w-100">
              <i className="fas fa-suitcase me-2"></i>
              Continue to Baggage Selection
            </button>
          </div>
          <div className="col-md-6">
            <button
              type="button"
              className="btn btn-outline-secondary w-100"
              onClick={handleSkipToPayment}
            >
              <i className="fas fa-credit-card me-2"></i>
              Skip to Payment
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PassengerDetails;
