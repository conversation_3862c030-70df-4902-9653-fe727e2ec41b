/* Design System for Tripstar Flight Booking Application */

:root {
  /* Color Palette */
  --primary: #0d6efd;
  --primary-dark: #0a58ca;
  --primary-light: #cfe2ff;
  --secondary: #6c757d;
  --secondary-light: #e9ecef;
  --success: #198754;
  --success-light: #d1e7dd;
  --danger: #dc3545;
  --danger-light: #f8d7da;
  --warning: #ffc107;
  --warning-light: #fff3cd;
  --info: #0dcaf0;
  --info-light: #cff4fc;
  --dark: #212529;
  --light: #f8f9fa;
  --white: #ffffff;

  /* Typography */
  --font-family-base:
    "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --font-family-heading:
    "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --line-height-base: 1.5;
  --line-height-sm: 1.25;
  --line-height-lg: 1.8;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* Border Radius */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-pill: 50rem;
  --border-radius-circle: 50%;

  /* Shadows */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

  /* Transitions */
  --transition-base: all 0.2s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;
  --transition-fast: all 0.1s ease-in-out;

  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #93c5fd;
  --secondary: #9ca3af;
  --secondary-light: #4b5563;
  --success: #10b981;
  --success-light: #065f46;
  --danger: #ef4444;
  --danger-light: #991b1b;
  --warning: #f59e0b;
  --warning-light: #92400e;
  --info: #06b6d4;
  --info-light: #0e7490;
  --dark: #e5e7eb;
  --light: #1f2937;
  --white: #111827;

  /* Additional dark theme variables */
  --body-bg: #0f172a;
  --body-color: #e5e7eb;
  --card-bg: #1e293b;
  --border-color: #334155;
  --input-bg: #1e293b;
  --input-color: #e5e7eb;
  --input-border: #475569;
}

/* Base Styles */
body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--dark);
  background-color: var(--light);
  transition: var(--transition-base);
}

body[data-theme="dark"] {
  color: var(--body-color);
  background-color: var(--body-bg);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-sm);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-xxl);
}

h2 {
  font-size: var(--font-size-xl);
}

h3 {
  font-size: var(--font-size-lg);
}

p {
  margin-bottom: var(--spacing-md);
}

/* Card Styles */
.card {
  background-color: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

body[data-theme="dark"] .card {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

/* Form Styles */
.form-control {
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--secondary-light);
  transition: var(--transition-base);
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem var(--primary-light);
}

body[data-theme="dark"] .form-control {
  background-color: var(--input-bg);
  color: var(--input-color);
  border-color: var(--input-border);
}

/* Button Styles */
.btn {
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-base);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: var(--success-dark);
  border-color: var(--success-dark);
}

/* Badge Styles */
.badge {
  border-radius: var(--border-radius-pill);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Utility Classes */
.shadow-hover {
  transition: var(--transition-base);
}

.shadow-hover:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rounded-custom {
  border-radius: var(--border-radius-md);
}

/* Infant Association Manager Styles */
.infant-association-manager {
  padding: 1.5rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.infant-association-item .card {
  border-left: 4px solid var(--primary);
  transition: all 0.3s ease;
}

.infant-association-item .card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.validation-message {
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.validation-message.text-success {
  background-color: rgba(40, 167, 69, 0.1);
}

.validation-message.text-danger {
  background-color: rgba(220, 53, 69, 0.1);
}

.infant-associations select option:disabled {
  color: #6c757d;
  font-style: italic;
  background-color: #e9ecef;
}

/* Animation for validation status changes */
.validation-message i {
  transition: transform 0.2s ease;
}

.validation-message.text-success i {
  transform: scale(1.2);
}

.validation-message.text-danger i {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

/* Responsive Breakpoints */
/* These are for reference, Bootstrap handles most of this */
/* 
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
*/
