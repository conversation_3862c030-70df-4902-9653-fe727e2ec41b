import React, { Component } from 'react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
    
    // You could also log to a monitoring service like Sentry here
    // if (process.env.NODE_ENV === 'production') {
    //   Sentry.captureException(error);
    // }
  }

  handleReset = () => {
    this.setState({ 
      hasError: false,
      error: null,
      errorInfo: null
    });
  }

  render() {
    const { hasError, error, errorInfo } = this.state;
    const { fallback, children } = this.props;

    if (hasError) {
      // You can render any custom fallback UI
      if (fallback) {
        return typeof fallback === 'function' 
          ? fallback({ error, errorInfo, reset: this.handleReset })
          : fallback;
      }

      return (
        <div className="error-boundary p-4 bg-light border rounded">
          <h2 className="text-danger mb-3">Something went wrong</h2>
          <p className="mb-3">We're sorry, but an error occurred while rendering this component.</p>
          
          <div className="mb-3">
            <button 
              className="btn btn-primary me-2" 
              onClick={this.handleReset}
            >
              Try Again
            </button>
            <button 
              className="btn btn-outline-secondary" 
              onClick={() => window.location.href = '/'}
            >
              Go to Home Page
            </button>
          </div>
          
          {process.env.NODE_ENV !== 'production' && (
            <div className="mt-4">
              <details className="error-details">
                <summary className="text-danger fw-bold">Error Details (Development Only)</summary>
                <pre className="mt-2 p-3 bg-dark text-light rounded">
                  {error?.toString()}
                  {errorInfo?.componentStack}
                </pre>
              </details>
            </div>
          )}
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;
