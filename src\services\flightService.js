import { request } from './apiClient';

/**
 * Search for flights
 * @param {Object} params - Search parameters
 * @param {boolean} useCache - Whether to use cache
 * @returns {Promise<Array>} Flight offers
 */
export const searchFlights = async (params, useCache = true) => {
  try {
    const response = await request({
      url: '/v2/shopping/flight-offers',
      method: 'get',
      params,
      mockData: { data: [] } // Mock data for development
    }, useCache);
    
    return response.data || [];
  } catch (error) {
    console.error('Flight search failed:', error);
    throw error;
  }
};

/**
 * Create a flight booking
 * @param {Object} flightOffers - Selected flight offers
 * @param {Array} travelers - Passenger details
 * @returns {Promise<Object>} Booking confirmation with PNR
 */
export const createBooking = async (flightOffers, travelers) => {
  try {
    // Step 1: Verify pricing (optional but recommended)
    const pricingResponse = await request({
      url: '/v1/shopping/flight-offers/pricing',
      method: 'post',
      data: {
        data: {
          type: 'flight-offers-pricing',
          flightOffers: flightOffers
        }
      }
    });

    // Use the verified flight offers from pricing response
    const verifiedFlightOffers = pricingResponse.data.flightOffers;

    // Step 2: Create the booking
    const bookingResponse = await request({
      url: '/v1/booking/flight-orders',
      method: 'post',
      data: {
        data: {
          type: 'flight-order',
          flightOffers: verifiedFlightOffers,
          travelers,
        }
      },
      mockData: {
        data: {
          id: 'DEV-' + Math.random().toString(36).substring(2, 10).toUpperCase(),
          associatedRecords: [{
            reference: 'TEST-PNR-' + Math.random().toString(36).substring(2, 8).toUpperCase()
          }]
        }
      }
    });

    // Extract PNR from response
    const bookingData = bookingResponse.data;
    const pnr = bookingData.associatedRecords?.[0]?.reference ||
                bookingData.id ||
                'PNR-' + Math.random().toString(36).substring(2, 10).toUpperCase();

    // Return enhanced booking data with easily accessible PNR
    return {
      ...bookingResponse,
      pnr: pnr
    };
  } catch (error) {
    console.error('Booking creation failed:', error);
    throw error;
  }
};

/**
 * Retrieve booking details by PNR
 * @param {string} pnr - The PNR/booking reference to retrieve
 * @returns {Promise<Object>} Booking details
 */
export const getBookingByPNR = async (pnr) => {
  if (!pnr) {
    throw new Error('PNR is required');
  }

  try {
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}`,
      method: 'get',
      mockData: {
        data: {
          id: pnr,
          type: 'flight-order',
          associatedRecords: [{
            reference: pnr,
            creationDate: new Date().toISOString(),
            originSystemCode: 'AMADEUS',
            flightOfferId: 'DEV-FLIGHT-OFFER'
          }],
          flightOffers: [],
          travelers: [],
          remarks: {
            general: [{
              subType: 'GENERAL_MISCELLANEOUS',
              text: 'MOCK BOOKING DATA FOR DEVELOPMENT'
            }]
          },
          ticketingAgreement: {
            option: 'DELAY_TO_CANCEL',
            delay: 'PT24H'
          },
          status: 'CONFIRMED'
        }
      }
    });

    // Return the booking data with enhanced PNR information
    return {
      ...response,
      pnr: pnr
    };
  } catch (error) {
    console.error('Booking retrieval failed:', error);
    throw error;
  }
};

/**
 * Search for locations (airports/cities)
 * @param {string} keyword - Search keyword
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array>} Array of location objects
 */
export const searchLocations = async (keyword, limit = 10) => {
  if (!keyword || keyword.length < 1) {
    return [];
  }

  try {
    const response = await request({
      url: '/v1/reference-data/locations',
      method: 'get',
      params: {
        keyword,
        subType: 'AIRPORT,CITY',
        'page[limit]': limit,
        view: 'LIGHT'
      },
      mockData: { data: [] }
    }, true, 24 * 60 * 60 * 1000); // Cache for 24 hours

    // Transform the response to a more usable format
    return response.data.map(location => ({
      id: location.id,
      code: location.iataCode,
      name: location.name,
      city: location.address?.cityName || location.name,
      country: location.address?.countryName || '',
      type: location.subType
    }));
  } catch (error) {
    console.error('Error searching locations:', error);
    return [];
  }
};
