/**
 * Hotel utility functions for formatting, validation, and data manipulation
 */

/**
 * Format hotel price for display
 * @param {Object} price - Price object from API
 * @param {string} currency - Currency code (default: BDT)
 * @returns {string} Formatted price string
 */
export const formatHotelPrice = (price, currency = 'BDT') => {
  if (!price || !price.total) {
    return 'Price not available';
  }
  
  const amount = parseFloat(price.total);
  const symbol = currency === 'BDT' ? '৳' : currency;
  
  return `${symbol}${amount.toLocaleString()}`;
};

/**
 * Calculate total price for multiple rooms
 * @param {Array} rooms - Array of room offers
 * @returns {number} Total price
 */
export const calculateTotalRoomPrice = (rooms) => {
  if (!rooms || !Array.isArray(rooms)) return 0;
  
  return rooms.reduce((total, room) => {
    const roomPrice = parseFloat(room.price?.total || 0);
    const quantity = room.quantity || 1;
    return total + (roomPrice * quantity);
  }, 0);
};

/**
 * Calculate number of nights between check-in and check-out dates
 * @param {string} checkInDate - Check-in date (YYYY-MM-DD)
 * @param {string} checkOutDate - Check-out date (YYYY-MM-DD)
 * @returns {number} Number of nights
 */
export const calculateNights = (checkInDate, checkOutDate) => {
  if (!checkInDate || !checkOutDate) return 1;
  
  const checkIn = new Date(checkInDate);
  const checkOut = new Date(checkOutDate);
  const diffTime = checkOut - checkIn;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(1, diffDays);
};

/**
 * Format date for display
 * @param {string} dateString - Date string (YYYY-MM-DD)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date
 */
export const formatHotelDate = (dateString, options = {}) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const defaultOptions = {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };
  
  return date.toLocaleDateString('en-US', { ...defaultOptions, ...options });
};

/**
 * Get star rating display
 * @param {string|number} rating - Hotel star rating
 * @returns {Array} Array of star objects for rendering
 */
export const getStarRating = (rating) => {
  const numRating = parseInt(rating || 0);
  const stars = [];
  
  for (let i = 1; i <= 5; i++) {
    stars.push({
      filled: i <= numRating,
      key: i
    });
  }
  
  return stars;
};

/**
 * Format amenity name for display
 * @param {string} amenity - Amenity code
 * @returns {string} Formatted amenity name
 */
export const formatAmenityName = (amenity) => {
  if (!amenity) return '';
  
  const amenityMap = {
    'WIFI': 'Free WiFi',
    'SWIMMING_POOL': 'Swimming Pool',
    'RESTAURANT': 'Restaurant',
    'SPA': 'Spa',
    'FITNESS_CENTER': 'Fitness Center',
    'BUSINESS_CENTER': 'Business Center',
    'CONCIERGE': 'Concierge',
    'ROOM_SERVICE': 'Room Service',
    'PARKING': 'Parking',
    'AIR_CONDITIONING': 'Air Conditioning',
    'LAUNDRY': 'Laundry Service',
    'ELEVATOR': 'Elevator',
    'SAFE': 'Safe',
    'MINIBAR': 'Minibar',
    'BALCONY': 'Balcony',
    'OCEAN_VIEW': 'Ocean View',
    'CITY_VIEW': 'City View',
    'MOUNTAIN_VIEW': 'Mountain View'
  };
  
  return amenityMap[amenity] || amenity.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Get room type display name
 * @param {string} roomType - Room type code
 * @returns {string} Formatted room type name
 */
export const formatRoomType = (roomType) => {
  if (!roomType) return 'Standard Room';
  
  const roomTypeMap = {
    'STANDARD_ROOM': 'Standard Room',
    'SUPERIOR_ROOM': 'Superior Room',
    'DELUXE_ROOM': 'Deluxe Room',
    'SUITE': 'Suite',
    'JUNIOR_SUITE': 'Junior Suite',
    'EXECUTIVE_ROOM': 'Executive Room',
    'FAMILY_ROOM': 'Family Room',
    'CONNECTING_ROOM': 'Connecting Room'
  };
  
  return roomTypeMap[roomType] || roomType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Get bed type display name
 * @param {string} bedType - Bed type code
 * @returns {string} Formatted bed type name
 */
export const formatBedType = (bedType) => {
  if (!bedType) return 'Standard';
  
  const bedTypeMap = {
    'KING': 'King Bed',
    'QUEEN': 'Queen Bed',
    'DOUBLE': 'Double Bed',
    'TWIN': 'Twin Bed',
    'SINGLE': 'Single Bed',
    'SOFA_BED': 'Sofa Bed'
  };
  
  return bedTypeMap[bedType] || bedType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Validate hotel search parameters
 * @param {Object} searchParams - Search parameters
 * @returns {Object} Validation result with isValid and errors
 */
export const validateHotelSearch = (searchParams) => {
  const errors = [];
  
  if (!searchParams.destination || !searchParams.destination.trim()) {
    errors.push('Destination is required');
  }
  
  if (!searchParams.checkInDate) {
    errors.push('Check-in date is required');
  }
  
  if (!searchParams.checkOutDate) {
    errors.push('Check-out date is required');
  }
  
  if (searchParams.checkInDate && searchParams.checkOutDate) {
    const checkIn = new Date(searchParams.checkInDate);
    const checkOut = new Date(searchParams.checkOutDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (checkIn < today) {
      errors.push('Check-in date cannot be in the past');
    }
    
    if (checkOut <= checkIn) {
      errors.push('Check-out date must be after check-in date');
    }
  }
  
  if (!searchParams.adults || searchParams.adults < 1) {
    errors.push('At least 1 adult is required');
  }
  
  if (!searchParams.rooms || searchParams.rooms < 1) {
    errors.push('At least 1 room is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate guest details
 * @param {Array} guests - Array of guest objects
 * @returns {Object} Validation result with isValid and errors
 */
export const validateGuestDetails = (guests) => {
  const errors = [];
  
  if (!guests || !Array.isArray(guests) || guests.length === 0) {
    errors.push('At least one guest is required');
    return { isValid: false, errors };
  }
  
  guests.forEach((guest, index) => {
    const guestNumber = index + 1;
    
    if (!guest.firstName || !guest.firstName.trim()) {
      errors.push(`Guest ${guestNumber}: First name is required`);
    }
    
    if (!guest.lastName || !guest.lastName.trim()) {
      errors.push(`Guest ${guestNumber}: Last name is required`);
    }
    
    if (!guest.dateOfBirth) {
      errors.push(`Guest ${guestNumber}: Date of birth is required`);
    } else {
      const birthDate = new Date(guest.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (guest.isMainGuest && age < 18) {
        errors.push(`Guest ${guestNumber}: Main guest must be at least 18 years old`);
      }
    }
    
    // Validate main guest contact details
    if (guest.isMainGuest) {
      if (!guest.email || !guest.email.trim()) {
        errors.push(`Guest ${guestNumber}: Email is required for main guest`);
      } else if (!/\S+@\S+\.\S+/.test(guest.email)) {
        errors.push(`Guest ${guestNumber}: Please enter a valid email address`);
      }
      
      if (!guest.phone || !guest.phone.trim()) {
        errors.push(`Guest ${guestNumber}: Phone number is required for main guest`);
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Generate hotel search ID for tracking
 * @param {Object} searchParams - Search parameters
 * @returns {string} Unique search ID
 */
export const generateHotelSearchId = (searchParams) => {
  const timestamp = Date.now();
  const destination = searchParams.destination?.replace(/\s+/g, '').toLowerCase() || 'unknown';
  const checkIn = searchParams.checkInDate?.replace(/-/g, '') || '';
  
  return `hotel_${destination}_${checkIn}_${timestamp}`;
};

/**
 * Filter hotels by criteria
 * @param {Array} hotels - Array of hotel offers
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered hotels
 */
export const filterHotels = (hotels, filters) => {
  if (!hotels || !Array.isArray(hotels)) return [];
  
  return hotels.filter(hotel => {
    // Price filter
    if (filters.priceRange) {
      const price = parseFloat(hotel.offers?.[0]?.price?.total || 0);
      if (price < filters.priceRange.min || price > filters.priceRange.max) {
        return false;
      }
    }
    
    // Star rating filter
    if (filters.starRating && filters.starRating.length > 0) {
      const rating = parseInt(hotel.hotel?.rating || 0);
      if (!filters.starRating.includes(rating)) {
        return false;
      }
    }
    
    // Amenities filter
    if (filters.amenities && filters.amenities.length > 0) {
      const hotelAmenities = hotel.hotel?.amenities || [];
      const hasRequiredAmenities = filters.amenities.some(amenity => 
        hotelAmenities.includes(amenity)
      );
      if (!hasRequiredAmenities) {
        return false;
      }
    }
    
    // Hotel chains filter
    if (filters.hotelChains && filters.hotelChains.length > 0) {
      if (!filters.hotelChains.includes(hotel.hotel?.chainCode)) {
        return false;
      }
    }
    
    return true;
  });
};

/**
 * Sort hotels by criteria
 * @param {Array} hotels - Array of hotel offers
 * @param {string} sortBy - Sort criteria (price, rating, name)
 * @returns {Array} Sorted hotels
 */
export const sortHotels = (hotels, sortBy) => {
  if (!hotels || !Array.isArray(hotels)) return [];
  
  const sortedHotels = [...hotels];
  
  switch (sortBy) {
    case 'price':
      return sortedHotels.sort((a, b) => {
        const priceA = parseFloat(a.offers?.[0]?.price?.total || 0);
        const priceB = parseFloat(b.offers?.[0]?.price?.total || 0);
        return priceA - priceB;
      });
      
    case 'rating':
      return sortedHotels.sort((a, b) => {
        const ratingA = parseInt(a.hotel?.rating || 0);
        const ratingB = parseInt(b.hotel?.rating || 0);
        return ratingB - ratingA;
      });
      
    case 'name':
      return sortedHotels.sort((a, b) => {
        const nameA = a.hotel?.name || '';
        const nameB = b.hotel?.name || '';
        return nameA.localeCompare(nameB);
      });
      
    default:
      return sortedHotels;
  }
};
