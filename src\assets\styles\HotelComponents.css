/* Hotel Components Styles */

/* Hotel Search Card */
.hotel-search-card {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.hotel-search-card .card-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-bottom: none;
}

/* Hotel Results */
.hotel-results .search-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
}

.hotel-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e9ecef;
}

.hotel-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.hotel-image {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hotel-name {
  color: #2c3e50;
  font-weight: 600;
}

.hotel-address {
  font-size: 0.9rem;
}

.star-rating {
  display: flex;
  gap: 2px;
}

.amenity-icon {
  color: #28a745;
  margin-right: 8px;
  font-size: 1.1rem;
}

.price-total {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
}

/* Hotel Filter Panel */
.filter-panel {
  position: sticky;
  top: 20px;
}

.filter-panel .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.price-range-filter .form-range {
  margin: 8px 0;
}

/* Hotel Details Modal */
.hotel-details .modal-dialog {
  max-width: 90vw;
}

.hotel-details .nav-tabs {
  border-bottom: 2px solid #dee2e6;
}

.hotel-details .nav-link {
  color: #6c757d;
  font-weight: 500;
}

.hotel-details .nav-link.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.hotel-info h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 8px;
}

/* Room Selection */
.room-selection .room-card {
  border: 1px solid #dee2e6;
  transition: border-color 0.2s ease;
}

.room-selection .room-card:hover {
  border-color: #007bff;
}

.room-image {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 8px;
}

.room-name {
  color: #2c3e50;
  font-weight: 600;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-selector button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.selected-total {
  background-color: #e7f3ff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #b3d9ff;
}

.continue-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
}

/* Guest Form */
.guest-section {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.guest-section h6 {
  color: #495057;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

/* Booking Confirmation */
.hotel-booking-confirmation .confirmation-number {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #b8daff;
}

.booking-summary {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.booking-actions .btn {
  margin-bottom: 8px;
}

.stay-detail strong {
  color: #495057;
  font-weight: 600;
}

.room-booking-detail {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 16px;
}

.room-booking-detail:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/* Progress Steps */
.booking-progress {
  margin-bottom: 2rem;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background-color: #007bff;
  color: white;
}

.step.completed .step-number {
  background-color: #28a745;
  color: white;
}

.step-label {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.step.active .step-label {
  color: #007bff;
  font-weight: 600;
}

.step.completed .step-label {
  color: #28a745;
}

.step-connector {
  width: 80px;
  height: 2px;
  background-color: #e9ecef;
  margin: 0 16px;
  margin-top: -20px;
}

.step.completed + .step-connector {
  background-color: #28a745;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hotel-card .row {
    flex-direction: column;
  }
  
  .hotel-image {
    height: 200px;
    margin-bottom: 16px;
  }
  
  .price-booking {
    text-align: center;
    margin-top: 16px;
  }
  
  .filter-panel {
    position: static;
    margin-bottom: 20px;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 16px;
  }
  
  .step-connector {
    width: 2px;
    height: 40px;
    margin: 8px 0;
  }
  
  .hotel-details .modal-dialog {
    max-width: 95vw;
    margin: 10px;
  }
}

@media (max-width: 576px) {
  .hotel-search-card .row {
    flex-direction: column;
  }
  
  .hotel-search-card .col-md-3,
  .hotel-search-card .col-md-4,
  .hotel-search-card .col-md-6 {
    margin-bottom: 16px;
  }
  
  .guest-section .row {
    flex-direction: column;
  }
  
  .guest-section .col-md-2,
  .guest-section .col-md-5,
  .guest-section .col-md-6 {
    margin-bottom: 12px;
  }
}

/* Dark Theme Support */
[data-theme="dark"] .hotel-search-card {
  background-color: #2c3e50;
  color: #ecf0f1;
}

[data-theme="dark"] .hotel-card {
  background-color: #34495e;
  border-color: #4a5f7a;
  color: #ecf0f1;
}

[data-theme="dark"] .hotel-name {
  color: #ecf0f1;
}

[data-theme="dark"] .filter-panel {
  background-color: #2c3e50;
  color: #ecf0f1;
}

[data-theme="dark"] .guest-section {
  background-color: #34495e;
  border-color: #4a5f7a;
  color: #ecf0f1;
}

[data-theme="dark"] .booking-summary {
  background-color: #34495e;
  color: #ecf0f1;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility Classes */
.cursor-pointer {
  cursor: pointer;
}

.hover-bg-light:hover {
  background-color: #f8f9fa !important;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.border-primary-light {
  border-color: #b3d9ff !important;
}

.bg-primary-light {
  background-color: #e7f3ff !important;
}
