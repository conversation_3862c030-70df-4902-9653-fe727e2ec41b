import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Badge, Accordion } from 'react-bootstrap';
import { FaFilter, FaTimes } from 'react-icons/fa';

const HotelFilterPanel = ({ hotels, onFiltersChange, initialFilters = {} }) => {
  const [filters, setFilters] = useState({
    priceRange: { min: 0, max: 50000 },
    starRating: [],
    amenities: [],
    hotelChains: [],
    ...initialFilters
  });

  const [priceRange, setPriceRange] = useState({ min: 0, max: 50000 });

  // Extract unique values from hotels for filter options
  useEffect(() => {
    if (hotels && hotels.length > 0) {
      // Calculate price range
      const prices = hotels
        .map(hotel => parseFloat(hotel.offers?.[0]?.price?.total || 0))
        .filter(price => price > 0);
      
      if (prices.length > 0) {
        const minPrice = Math.floor(Math.min(...prices));
        const maxPrice = Math.ceil(Math.max(...prices));
        setPriceRange({ min: minPrice, max: maxPrice });
        
        // Update filters if not set
        if (filters.priceRange.min === 0 && filters.priceRange.max === 50000) {
          setFilters(prev => ({
            ...prev,
            priceRange: { min: minPrice, max: maxPrice }
          }));
        }
      }
    }
  }, [hotels]);

  // Get unique amenities from all hotels
  const getUniqueAmenities = () => {
    if (!hotels) return [];
    const allAmenities = hotels.reduce((acc, hotel) => {
      if (hotel.hotel?.amenities) {
        acc.push(...hotel.hotel.amenities);
      }
      return acc;
    }, []);
    return [...new Set(allAmenities)].sort();
  };

  // Get unique hotel chains
  const getUniqueChains = () => {
    if (!hotels) return [];
    const chains = hotels
      .map(hotel => hotel.hotel?.chainCode)
      .filter(chain => chain)
      .reduce((acc, chain) => {
        if (!acc.find(c => c.code === chain)) {
          acc.push({ code: chain, name: getChainName(chain) });
        }
        return acc;
      }, []);
    return chains.sort((a, b) => a.name.localeCompare(b.name));
  };

  // Get chain name from code (simplified mapping)
  const getChainName = (code) => {
    const chainNames = {
      'RT': 'Resort Hotels',
      'HI': 'Holiday Inn',
      'MA': 'Marriott',
      'AC': 'Accor',
      'IH': 'InterContinental',
      'HY': 'Hyatt'
    };
    return chainNames[code] || code;
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters };

    switch (filterType) {
      case 'priceRange':
        newFilters.priceRange = value;
        break;
      case 'starRating':
        if (newFilters.starRating.includes(value)) {
          newFilters.starRating = newFilters.starRating.filter(rating => rating !== value);
        } else {
          newFilters.starRating.push(value);
        }
        break;
      case 'amenities':
        if (newFilters.amenities.includes(value)) {
          newFilters.amenities = newFilters.amenities.filter(amenity => amenity !== value);
        } else {
          newFilters.amenities.push(value);
        }
        break;
      case 'hotelChains':
        if (newFilters.hotelChains.includes(value)) {
          newFilters.hotelChains = newFilters.hotelChains.filter(chain => chain !== value);
        } else {
          newFilters.hotelChains.push(value);
        }
        break;
      default:
        break;
    }

    setFilters(newFilters);
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  // Clear all filters
  const clearAllFilters = () => {
    const clearedFilters = {
      priceRange: priceRange,
      starRating: [],
      amenities: [],
      hotelChains: []
    };
    setFilters(clearedFilters);
    if (onFiltersChange) {
      onFiltersChange(clearedFilters);
    }
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.starRating.length > 0) count++;
    if (filters.amenities.length > 0) count++;
    if (filters.hotelChains.length > 0) count++;
    if (filters.priceRange.min !== priceRange.min || filters.priceRange.max !== priceRange.max) count++;
    return count;
  };

  // Format amenity name for display
  const formatAmenityName = (amenity) => {
    return amenity.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const uniqueAmenities = getUniqueAmenities();
  const uniqueChains = getUniqueChains();
  const activeFilterCount = getActiveFilterCount();

  return (
    <Card className="filter-panel">
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h6 className="mb-0">
          <FaFilter className="me-2" />
          Filters
          {activeFilterCount > 0 && (
            <Badge bg="primary" className="ms-2">
              {activeFilterCount}
            </Badge>
          )}
        </h6>
        {activeFilterCount > 0 && (
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={clearAllFilters}
          >
            <FaTimes className="me-1" />
            Clear All
          </Button>
        )}
      </Card.Header>
      <Card.Body className="p-0">
        <Accordion defaultActiveKey={['0', '1']} alwaysOpen>
          {/* Price Range */}
          <Accordion.Item eventKey="0">
            <Accordion.Header>Price Range (per night)</Accordion.Header>
            <Accordion.Body>
              <div className="price-range-filter">
                <div className="d-flex justify-content-between mb-2">
                  <small>৳{filters.priceRange.min.toLocaleString()}</small>
                  <small>৳{filters.priceRange.max.toLocaleString()}</small>
                </div>
                <Form.Range
                  min={priceRange.min}
                  max={priceRange.max}
                  value={filters.priceRange.min}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    min: parseInt(e.target.value)
                  })}
                  className="mb-2"
                />
                <Form.Range
                  min={priceRange.min}
                  max={priceRange.max}
                  value={filters.priceRange.max}
                  onChange={(e) => handleFilterChange('priceRange', {
                    ...filters.priceRange,
                    max: parseInt(e.target.value)
                  })}
                />
                <div className="d-flex gap-2 mt-2">
                  <Form.Control
                    type="number"
                    size="sm"
                    placeholder="Min"
                    value={filters.priceRange.min}
                    onChange={(e) => handleFilterChange('priceRange', {
                      ...filters.priceRange,
                      min: parseInt(e.target.value) || 0
                    })}
                  />
                  <Form.Control
                    type="number"
                    size="sm"
                    placeholder="Max"
                    value={filters.priceRange.max}
                    onChange={(e) => handleFilterChange('priceRange', {
                      ...filters.priceRange,
                      max: parseInt(e.target.value) || priceRange.max
                    })}
                  />
                </div>
              </div>
            </Accordion.Body>
          </Accordion.Item>

          {/* Star Rating */}
          <Accordion.Item eventKey="1">
            <Accordion.Header>Star Rating</Accordion.Header>
            <Accordion.Body>
              <div className="star-rating-filter">
                {[5, 4, 3, 2, 1].map(rating => (
                  <Form.Check
                    key={rating}
                    type="checkbox"
                    id={`star-${rating}`}
                    label={`${rating} Star${rating !== 1 ? 's' : ''}`}
                    checked={filters.starRating.includes(rating)}
                    onChange={() => handleFilterChange('starRating', rating)}
                    className="mb-2"
                  />
                ))}
              </div>
            </Accordion.Body>
          </Accordion.Item>

          {/* Amenities */}
          {uniqueAmenities.length > 0 && (
            <Accordion.Item eventKey="2">
              <Accordion.Header>Amenities</Accordion.Header>
              <Accordion.Body>
                <div className="amenities-filter">
                  {uniqueAmenities.slice(0, 8).map(amenity => (
                    <Form.Check
                      key={amenity}
                      type="checkbox"
                      id={`amenity-${amenity}`}
                      label={formatAmenityName(amenity)}
                      checked={filters.amenities.includes(amenity)}
                      onChange={() => handleFilterChange('amenities', amenity)}
                      className="mb-2"
                    />
                  ))}
                  {uniqueAmenities.length > 8 && (
                    <small className="text-muted">
                      +{uniqueAmenities.length - 8} more amenities available
                    </small>
                  )}
                </div>
              </Accordion.Body>
            </Accordion.Item>
          )}

          {/* Hotel Chains */}
          {uniqueChains.length > 0 && (
            <Accordion.Item eventKey="3">
              <Accordion.Header>Hotel Chains</Accordion.Header>
              <Accordion.Body>
                <div className="hotel-chains-filter">
                  {uniqueChains.map(chain => (
                    <Form.Check
                      key={chain.code}
                      type="checkbox"
                      id={`chain-${chain.code}`}
                      label={chain.name}
                      checked={filters.hotelChains.includes(chain.code)}
                      onChange={() => handleFilterChange('hotelChains', chain.code)}
                      className="mb-2"
                    />
                  ))}
                </div>
              </Accordion.Body>
            </Accordion.Item>
          )}
        </Accordion>
      </Card.Body>
    </Card>
  );
};

export default HotelFilterPanel;
