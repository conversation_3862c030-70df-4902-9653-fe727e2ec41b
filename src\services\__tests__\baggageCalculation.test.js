import { PriceCalculationService } from '../priceCalculationService';

describe('Baggage Calculation Tests', () => {
  test('should calculate baggage cost correctly for single passenger', () => {
    const baggageSelections = {
      outbound: {
        'passenger-1': 'checked20'
      },
      return: {
        'passenger-1': 'checked20'
      }
    };

    const cost = PriceCalculationService.calculateBaggageCost(baggageSelections);
    expect(cost).toBe(4000); // 2000 + 2000
  });

  test('should calculate baggage cost for multiple passengers', () => {
    const baggageSelections = {
      outbound: {
        'passenger-1': 'checked20',
        'passenger-2': 'carry',
        'passenger-3': 'checked30'
      },
      return: {
        'passenger-1': 'checked15',
        'passenger-2': 'carry',
        'passenger-3': 'excess'
      }
    };

    const cost = PriceCalculationService.calculateBaggageCost(baggageSelections);
    // Outbound: 2000 + 0 + 3000 = 5000
    // Return: 1500 + 0 + 4000 = 5500
    // Total: 10500
    expect(cost).toBe(10500);
  });

  test('should return 0 for no baggage selections', () => {
    const cost = PriceCalculationService.calculateBaggageCost(null);
    expect(cost).toBe(0);
  });

  test('should return 0 for carry-on only selections', () => {
    const baggageSelections = {
      outbound: {
        'passenger-1': 'carry',
        'passenger-2': 'none'
      },
      return: {
        'passenger-1': 'carry',
        'passenger-2': 'carry'
      }
    };

    const cost = PriceCalculationService.calculateBaggageCost(baggageSelections);
    expect(cost).toBe(0);
  });

  test('should include baggage cost in total price calculation', () => {
    const flights = {
      outbound: { price: { total: 10000, currency: 'BDT' } },
      return: { price: { total: 10000, currency: 'BDT' } }
    };

    const passengers = { adult: 1, child: 0, infant: 0 };
    
    const baggageSelections = {
      outbound: { 'passenger-1': 'checked20' },
      return: { 'passenger-1': 'checked20' }
    };

    const priceDetails = PriceCalculationService.calculateTotalPrice({
      flights,
      passengers,
      pricingOption: 'basic',
      baggageSelections
    });

    expect(priceDetails.baggageTotal).toBe(4000);
    expect(priceDetails.total).toBeGreaterThan(20000); // Base price + baggage
  });

  test('should handle one-way flights correctly', () => {
    const baggageSelections = {
      outbound: {
        'passenger-1': 'checked25'
      },
      return: null
    };

    const cost = PriceCalculationService.calculateBaggageCost(baggageSelections);
    expect(cost).toBe(2500); // Only outbound baggage
  });
});
