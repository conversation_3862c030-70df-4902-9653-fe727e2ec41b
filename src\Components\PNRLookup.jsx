import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getBookingByPNR } from '../services/apiService';
import '../assets/styles/PNRLookup.css';

/**
 * Component for looking up booking details by PNR
 */
const PNRLookup = () => {
  const [pnr, setPnr] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!pnr.trim()) {
      setError('Please enter a valid PNR/Booking Reference');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const bookingData = await getBookingByPNR(pnr.trim());
      
      // Navigate to booking confirmation page with the retrieved data
      navigate('/booking-confirm', {
        state: {
          bookingData,
          fromPNRLookup: true
        }
      });
    } catch (err) {
      console.error('Error retrieving booking:', err);
      setError('Unable to find booking with the provided PNR. Please check and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pnr-lookup-container">
      <div className="card shadow-sm">
        <div className="card-header bg-primary text-white">
          <h4 className="mb-0">Retrieve Your Booking</h4>
        </div>
        <div className="card-body">
          <p className="text-muted mb-4">
            Enter your PNR (Booking Reference) to retrieve your booking details.
          </p>

          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label htmlFor="pnr-input" className="form-label fw-bold">
                PNR / Booking Reference
              </label>
              <input
                type="text"
                id="pnr-input"
                className={`form-control form-control-lg ${error ? 'is-invalid' : ''}`}
                value={pnr}
                onChange={(e) => setPnr(e.target.value.toUpperCase())}
                placeholder="e.g. ABC123"
                maxLength="8"
                autoComplete="off"
                required
              />
              {error && <div className="invalid-feedback">{error}</div>}
              <small className="form-text text-muted">
                The PNR is a 6-8 character code provided in your booking confirmation.
              </small>
            </div>

            <div className="d-grid gap-2 mt-4">
              <button
                type="submit"
                className="btn btn-primary btn-lg"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Retrieving...
                  </>
                ) : (
                  'Retrieve Booking'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default PNRLookup;
