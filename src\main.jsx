import React, { lazy, Suspense } from "react";
import ReactDOM from "react-dom/client";
import { <PERSON>rowser<PERSON>outer, Routes, Route } from "react-router-dom";
import { AppProvider } from "./context/AppContext";
import App from "./App.jsx";
import "./index.css";
import "./assets/styles/improvedFlightSearch.css";

// Lazy load components for code splitting
const Home = lazy(() => import("./pages/Home"));
const Flights = lazy(() => import("./pages/Flights"));
const Hotels = lazy(() => import("./pages/Hotels"));
const Contact = lazy(() => import("./pages/Contact"));
const BookingPage = lazy(() => import("./pages/BookingPage"));
const BookingConfirm = lazy(() => import("./Components/BookingConfirm.jsx"));
const PNRLookup = lazy(() => import("./Components/PNRLookup.jsx"));
const SessionExpired = lazy(() => import("./pages/SessionExpired"));

// Loading component for suspense fallback
const LoadingFallback = () => (
  <div
    className="loading-container d-flex justify-content-center align-items-center"
    style={{ minHeight: "300px" }}
  >
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  </div>
);

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <AppProvider>
      <BrowserRouter>
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            <Route path="/" element={<App />}>
              <Route index element={<Home />} />
              <Route path="flights" element={<Flights />} />
              <Route path="hotels" element={<Hotels />} />
              <Route path="contact" element={<Contact />} />
              <Route path="booking" element={<BookingPage />} />
              <Route path="booking-confirm" element={<BookingConfirm />} />
              <Route path="retrieve-booking" element={<PNRLookup />} />
              <Route path="session-expired" element={<SessionExpired />} />
            </Route>
          </Routes>
        </Suspense>
      </BrowserRouter>
    </AppProvider>
  </React.StrictMode>
);
