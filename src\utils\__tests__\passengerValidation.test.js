import { validate<PERSON>assenger } from "../passengerValidation";

describe("validatePassenger", () => {
  const today = new Date();
  const validAdult = {
    firstName: "<PERSON>",
    lastName: "Doe",
    passport: "AB123456",
    passportExpiry: new Date(
      today.getFullYear() + 1,
      today.getMonth(),
      today.getDate()
    )
      .toISOString()
      .split("T")[0],
    dob: new Date(today.getFullYear() - 30, today.getMonth(), today.getDate())
      .toISOString()
      .split("T")[0],
    email: "<EMAIL>",
    phone: "+1234567890",
    gender: "MALE",
    type: "ADULT",
  };

  describe("name validation", () => {
    test("validates correct names", () => {
      const passenger = { ...validAdult };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.firstName).toBeUndefined();
      expect(errors.lastName).toBeUndefined();
    });

    test("rejects invalid name characters", () => {
      const passenger = {
        ...validAdult,
        firstName: "John123",
        lastName: "Doe#$%",
      };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.firstName).toBeDefined();
      expect(errors.lastName).toBeDefined();
    });

    test("accepts valid special characters in names", () => {
      const passenger = {
        ...validAdult,
        firstName: "Mary-Jane",
        lastName: "O'Connor",
      };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.firstName).toBeUndefined();
      expect(errors.lastName).toBeUndefined();
    });
  });

  describe("passport validation", () => {
    test("validates correct passport format", () => {
      const passenger = { ...validAdult };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.passport).toBeUndefined();
    });

    test("requires passport for adults", () => {
      const passenger = { ...validAdult, passport: "" };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.passport).toBeDefined();
    });

    test("makes passport optional for infants", () => {
      const passenger = {
        ...validAdult,
        type: "INFANT",
        passport: "",
        associatedAdult: 1,
      };
      const errors = validatePassenger(passenger, [validAdult, passenger]);
      expect(errors.passport).toBeUndefined();
    });

    test("validates passport expiry", () => {
      const passenger = {
        ...validAdult,
        passportExpiry: new Date(
          today.getFullYear(),
          today.getMonth() - 1,
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
      };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.passportExpiry).toBeDefined();
    });
  });

  describe("age validation", () => {
    test("validates adult age", () => {
      const passenger = {
        ...validAdult,
        dob: new Date(
          today.getFullYear() - 17,
          today.getMonth(),
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
      };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.dob).toBeDefined();
    });

    test("validates child age", () => {
      const passenger = {
        ...validAdult,
        type: "CHILD",
        dob: new Date(
          today.getFullYear() - 13,
          today.getMonth(),
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
      };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.dob).toBeDefined();
    });

    test("validates infant age", () => {
      const passenger = {
        ...validAdult,
        type: "INFANT",
        dob: new Date(
          today.getFullYear() - 3,
          today.getMonth(),
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
        associatedAdult: 1,
      };
      const errors = validatePassenger(passenger, [validAdult, passenger]);
      expect(errors.dob).toBeDefined();
    });
  });

  describe("contact information validation", () => {
    test("validates correct email format", () => {
      const passenger = { ...validAdult };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.email).toBeUndefined();
    });

    test("rejects invalid email format", () => {
      const passenger = { ...validAdult, email: "invalid.email" };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.email).toBeDefined();
    });

    test("validates correct phone format", () => {
      const passenger = { ...validAdult };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.phone).toBeUndefined();
    });

    test("rejects invalid phone format", () => {
      const passenger = { ...validAdult, phone: "123" };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.phone).toBeDefined();
    });
  });

  describe("infant-adult association validation", () => {
    test("requires infant to be associated with an adult", () => {
      const passenger = {
        ...validAdult,
        type: "INFANT",
        dob: new Date(
          today.getFullYear(),
          today.getMonth() - 6,
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
      };
      const errors = validatePassenger(passenger, [validAdult, passenger]);
      expect(errors.associatedAdult).toBeDefined();
    });

    test("validates adult age for infant association", () => {
      const youngAdult = {
        ...validAdult,
        dob: new Date(
          today.getFullYear() - 17,
          today.getMonth(),
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
      };
      const infant = {
        ...validAdult,
        type: "INFANT",
        dob: new Date(
          today.getFullYear(),
          today.getMonth() - 6,
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
        associatedAdult: 1,
      };
      const errors = validatePassenger(infant, [youngAdult, infant]);
      expect(errors.associatedAdult).toBeDefined();
    });

    test("prevents multiple infants per adult", () => {
      const adult = validAdult;
      const infant1 = {
        ...validAdult,
        type: "INFANT",
        dob: new Date(
          today.getFullYear(),
          today.getMonth() - 6,
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
        associatedAdult: 1,
      };
      const infant2 = {
        ...validAdult,
        type: "INFANT",
        dob: new Date(
          today.getFullYear(),
          today.getMonth() - 4,
          today.getDate()
        )
          .toISOString()
          .split("T")[0],
        associatedAdult: 1,
      };
      const errors = validatePassenger(infant2, [adult, infant1, infant2]);
      expect(errors.associatedAdult).toBeDefined();
    });
  });

  describe("special requirements validation", () => {
    test("validates meal preference", () => {
      const passenger = { ...validAdult, mealPreference: "INVALID_MEAL" };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.mealPreference).toBeDefined();
    });

    test("validates wheelchair assistance", () => {
      const passenger = { ...validAdult, wheelchair: "INVALID_CODE" };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.wheelchair).toBeDefined();
    });

    test("validates special needs length", () => {
      const passenger = { ...validAdult, specialNeeds: "a".repeat(501) };
      const errors = validatePassenger(passenger, [passenger]);
      expect(errors.specialNeeds).toBeDefined();
    });
  });
});
