import React, { useState } from "react";
import { Container, Alert } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "../context/AppContext";
import HotelGuestForm from "../Components/HotelGuestForm";
import Payment from "../features/payment/Payment";
import HotelBookingConfirmation from "../Components/HotelBookingConfirmation";
import {
  createHotelBooking,
  formatHotelGuests,
} from "../services/hotelService";
import { processPayment } from "../services/apiService";
import "../assets/styles/Pages.css";

const HotelBooking = () => {
  const navigate = useNavigate();
  const { state, dispatch } = useAppContext();

  // Get booking data from state (passed from hotel search/selection)
  const hotelBookingData = state.hotelBookingData || {};
  const { hotelOffer, selectedRooms, searchParams } = hotelBookingData;

  const [currentStep, setCurrentStep] = useState("guest-details"); // guest-details, payment, confirmation
  const [guestDetails, setGuestDetails] = useState(null);
  const [paymentDetails, setPaymentDetails] = useState(null);
  const [bookingConfirmation, setBookingConfirmation] = useState(null);
  const [error, setError] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Redirect if no booking data
  if (!hotelOffer || !selectedRooms) {
    return (
      <Container className="py-5">
        <Alert variant="warning" className="text-center">
          <h5>No hotel booking data found</h5>
          <p>Please start a new hotel search to make a booking.</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate("/hotels")}
          >
            Search Hotels
          </button>
        </Alert>
      </Container>
    );
  }

  // Calculate total price
  const calculateTotalPrice = () => {
    return selectedRooms.reduce((total, room) => {
      return total + parseFloat(room.price?.total || 0) * (room.quantity || 1);
    }, 0);
  };

  // Handle guest details submission
  const handleGuestDetailsSubmit = async (guests) => {
    try {
      setGuestDetails(guests);
      setCurrentStep("payment");
      setError("");
    } catch (err) {
      console.error("Guest details submission failed:", err);
      setError("Failed to save guest details. Please try again.");
    }
  };

  // Handle payment completion (called by Payment component)
  const handlePaymentComplete = async (transactionId) => {
    setIsProcessing(true);
    setError("");

    try {
      console.log("Payment completed with transaction ID:", transactionId);

      // Create hotel booking
      const formattedGuests = formatHotelGuests(guestDetails);
      const bookingResult = await createHotelBooking(
        hotelOffer,
        formattedGuests,
        {
          transactionId: transactionId,
          amount: calculateTotalPrice(),
          currency: "BDT",
        }
      );

      setBookingConfirmation(bookingResult);
      setCurrentStep("confirmation");

      // Clear hotel booking data from state
      dispatch({
        type: "SAVE_FORM_DATA",
        payload: {
          ...state.formData,
          hotelBookingData: null,
        },
      });

      console.log("Hotel booking created successfully:", bookingResult);
    } catch (err) {
      console.error("Hotel booking creation failed:", err);
      setError(err.message || "Hotel booking failed. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    switch (currentStep) {
      case "payment":
        setCurrentStep("guest-details");
        break;
      case "guest-details":
        navigate("/hotels");
        break;
      default:
        navigate("/hotels");
    }
  };

  // Handle new search from confirmation
  const handleNewSearch = () => {
    navigate("/hotels");
  };

  // Handle view bookings from confirmation
  const handleViewBookings = () => {
    // Navigate to bookings page (you might need to create this)
    navigate("/bookings");
  };

  return (
    <Container className="py-4">
      {error && (
        <Alert variant="danger" dismissible onClose={() => setError("")}>
          {error}
        </Alert>
      )}

      {/* Progress Indicator */}
      <div className="booking-progress mb-4">
        <div className="d-flex justify-content-center">
          <div className="progress-steps d-flex align-items-center">
            <div
              className={`step ${currentStep === "guest-details" ? "active" : currentStep !== "guest-details" ? "completed" : ""}`}
            >
              <span className="step-number">1</span>
              <span className="step-label">Guest Details</span>
            </div>
            <div className="step-connector"></div>
            <div
              className={`step ${currentStep === "payment" ? "active" : currentStep === "confirmation" ? "completed" : ""}`}
            >
              <span className="step-number">2</span>
              <span className="step-label">Payment</span>
            </div>
            <div className="step-connector"></div>
            <div
              className={`step ${currentStep === "confirmation" ? "active" : ""}`}
            >
              <span className="step-number">3</span>
              <span className="step-label">Confirmation</span>
            </div>
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === "guest-details" && (
        <HotelGuestForm
          hotelOffer={hotelOffer}
          selectedRooms={selectedRooms}
          searchParams={searchParams}
          onGuestDetailsSubmit={handleGuestDetailsSubmit}
          onBack={handleBack}
        />
      )}

      {currentStep === "payment" && (
        <Payment
          totalAmount={calculateTotalPrice()}
          onPaymentSuccess={handlePaymentComplete}
        />
      )}

      {currentStep === "confirmation" && bookingConfirmation && (
        <HotelBookingConfirmation
          bookingData={bookingConfirmation}
          hotelOffer={hotelOffer}
          selectedRooms={selectedRooms}
          guests={guestDetails}
          searchParams={searchParams}
          onNewSearch={handleNewSearch}
          onViewBookings={handleViewBookings}
        />
      )}
    </Container>
  );
};

export default HotelBooking;
