import React, { useState } from 'react';
import { Card, Row, Col, Button, Badge, Alert, Form } from 'react-bootstrap';
import { FaBed, FaUsers, FaWifi, FaCheck, FaTimes } from 'react-icons/fa';

const RoomSelection = ({ hotelOffer, searchParams, onRoomSelect, onBack }) => {
  const [selectedRooms, setSelectedRooms] = useState({});
  const [totalPrice, setTotalPrice] = useState(0);

  const hotel = hotelOffer?.hotel;
  const offers = hotelOffer?.offers || [];

  // Handle room selection
  const handleRoomSelect = (offerId, quantity) => {
    const newSelectedRooms = { ...selectedRooms };
    
    if (quantity === 0) {
      delete newSelectedRooms[offerId];
    } else {
      newSelectedRooms[offerId] = {
        offer: offers.find(offer => offer.id === offerId),
        quantity: quantity
      };
    }
    
    setSelectedRooms(newSelectedRooms);
    
    // Calculate total price
    const newTotalPrice = Object.values(newSelectedRooms).reduce((total, room) => {
      return total + (parseFloat(room.offer.price?.total || 0) * room.quantity);
    }, 0);
    
    setTotalPrice(newTotalPrice);
  };

  // Get selected room count
  const getSelectedRoomCount = () => {
    return Object.values(selectedRooms).reduce((total, room) => total + room.quantity, 0);
  };

  // Check if selection is valid
  const isSelectionValid = () => {
    const selectedCount = getSelectedRoomCount();
    const requiredRooms = searchParams?.roomQuantity || 1;
    return selectedCount === requiredRooms;
  };

  // Handle continue to booking
  const handleContinue = () => {
    if (isSelectionValid() && onRoomSelect) {
      const selectedOffers = Object.values(selectedRooms).map(room => ({
        ...room.offer,
        quantity: room.quantity
      }));
      onRoomSelect(hotelOffer, selectedOffers);
    }
  };

  // Format price
  const formatPrice = (price) => {
    if (!price) return 'Price not available';
    return `৳${parseFloat(price.total).toLocaleString()}`;
  };

  // Get room amenities
  const getRoomAmenities = (offer) => {
    // This would typically come from the API, but for now we'll use some common amenities
    const commonAmenities = ['WIFI', 'AIR_CONDITIONING', 'ROOM_SERVICE'];
    return commonAmenities;
  };

  // Render amenity icon
  const renderAmenityIcon = (amenity) => {
    const amenityIcons = {
      WIFI: <FaWifi />,
      AIR_CONDITIONING: <FaCheck />,
      ROOM_SERVICE: <FaUsers />,
    };
    return amenityIcons[amenity] || <FaCheck />;
  };

  // Calculate nights
  const calculateNights = () => {
    if (searchParams?.checkInDate && searchParams?.checkOutDate) {
      const checkIn = new Date(searchParams.checkInDate);
      const checkOut = new Date(searchParams.checkOutDate);
      const diffTime = checkOut - checkIn;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }
    return 1;
  };

  if (!hotel || offers.length === 0) {
    return (
      <Alert variant="warning">
        <h5>No rooms available</h5>
        <p>Sorry, no rooms are available for the selected dates.</p>
        <Button variant="outline-primary" onClick={onBack}>
          Back to Search
        </Button>
      </Alert>
    );
  }

  const requiredRooms = searchParams?.roomQuantity || 1;
  const selectedCount = getSelectedRoomCount();

  return (
    <div className="room-selection">
      {/* Hotel Header */}
      <Card className="mb-4">
        <Card.Body>
          <Row className="align-items-center">
            <Col md={8}>
              <h4 className="mb-1">{hotel.name}</h4>
              <p className="text-muted mb-0">
                {hotel.address?.cityName}, {hotel.address?.countryCode}
              </p>
              <small className="text-muted">
                {searchParams?.checkInDate} to {searchParams?.checkOutDate} • 
                {calculateNights()} night{calculateNights() !== 1 ? 's' : ''} • 
                {searchParams?.adults} adult{searchParams?.adults !== 1 ? 's' : ''}
              </small>
            </Col>
            <Col md={4} className="text-end">
              <Button variant="outline-secondary" onClick={onBack}>
                Back to Results
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Room Selection Status */}
      <Alert variant={isSelectionValid() ? 'success' : 'info'} className="mb-4">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <strong>
              {selectedCount} of {requiredRooms} room{requiredRooms !== 1 ? 's' : ''} selected
            </strong>
            {!isSelectionValid() && (
              <div className="mt-1">
                <small>
                  Please select {requiredRooms - selectedCount} more room{(requiredRooms - selectedCount) !== 1 ? 's' : ''}
                </small>
              </div>
            )}
          </div>
          {totalPrice > 0 && (
            <div className="text-end">
              <h5 className="mb-0 text-primary">
                ৳{totalPrice.toLocaleString()}
              </h5>
              <small className="text-muted">Total</small>
            </div>
          )}
        </div>
      </Alert>

      {/* Available Rooms */}
      <div className="available-rooms">
        <h5 className="mb-3">Available Rooms</h5>
        
        {offers.map((offer, index) => {
          const selectedQuantity = selectedRooms[offer.id]?.quantity || 0;
          const roomAmenities = getRoomAmenities(offer);
          
          return (
            <Card key={index} className="room-card mb-3">
              <Card.Body>
                <Row>
                  {/* Room Image Placeholder */}
                  <Col md={3}>
                    <div 
                      className="room-image bg-light d-flex align-items-center justify-content-center rounded"
                      style={{ height: '150px', backgroundColor: '#f8f9fa' }}
                    >
                      <FaBed size={30} className="text-muted" />
                    </div>
                  </Col>

                  {/* Room Details */}
                  <Col md={6}>
                    <div className="room-details">
                      <h6 className="room-name mb-2">
                        {offer.room?.description?.text || 'Standard Room'}
                      </h6>
                      
                      <div className="room-info mb-2">
                        <Badge bg="secondary" className="me-2">
                          {offer.room?.typeEstimated?.category?.replace('_', ' ') || 'Room'}
                        </Badge>
                        {offer.room?.typeEstimated?.bedType && (
                          <Badge bg="outline-secondary" className="me-2">
                            {offer.room.typeEstimated.bedType} Bed
                          </Badge>
                        )}
                        <Badge bg="outline-secondary">
                          <FaUsers className="me-1" />
                          {offer.guests?.adults || searchParams?.adults} Guest{(offer.guests?.adults || searchParams?.adults) !== 1 ? 's' : ''}
                        </Badge>
                      </div>

                      {/* Room Amenities */}
                      <div className="room-amenities mb-2">
                        <small className="text-muted">Amenities: </small>
                        {roomAmenities.map((amenity, idx) => (
                          <span key={idx} className="amenity-icon me-2" title={amenity.replace('_', ' ')}>
                            {renderAmenityIcon(amenity)}
                          </span>
                        ))}
                      </div>

                      {/* Cancellation Policy */}
                      {offer.policies?.cancellation && (
                        <div className="cancellation-policy">
                          <Badge 
                            bg={offer.policies.cancellation.type === 'FREE_CANCELLATION' ? 'success' : 'warning'}
                            className="me-2"
                          >
                            {offer.policies.cancellation.type === 'FREE_CANCELLATION' ? 
                              'Free Cancellation' : 'Cancellation Fee Applies'}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </Col>

                  {/* Price and Selection */}
                  <Col md={3} className="text-end">
                    <div className="price-selection">
                      <div className="price-info mb-3">
                        <h5 className="text-primary mb-0">
                          {formatPrice(offer.price)}
                        </h5>
                        <small className="text-muted">per night</small>
                        
                        {offer.price?.taxes && offer.price.taxes.length > 0 && (
                          <div className="mt-1">
                            <small className="text-muted">
                              +৳{offer.price.taxes.reduce((sum, tax) => sum + parseFloat(tax.amount), 0).toFixed(2)} taxes
                            </small>
                          </div>
                        )}
                      </div>

                      {/* Room Quantity Selector */}
                      <div className="quantity-selector">
                        <Form.Label className="small">Rooms:</Form.Label>
                        <div className="d-flex align-items-center">
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => handleRoomSelect(offer.id, Math.max(0, selectedQuantity - 1))}
                            disabled={selectedQuantity === 0}
                          >
                            -
                          </Button>
                          <span className="mx-3 fw-bold">{selectedQuantity}</span>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            onClick={() => handleRoomSelect(offer.id, selectedQuantity + 1)}
                            disabled={selectedCount >= requiredRooms && selectedQuantity === 0}
                          >
                            +
                          </Button>
                        </div>
                      </div>

                      {selectedQuantity > 0 && (
                        <div className="selected-total mt-2">
                          <small className="text-muted">
                            {selectedQuantity} × {formatPrice(offer.price)} = 
                          </small>
                          <div className="fw-bold text-primary">
                            ৳{(parseFloat(offer.price?.total || 0) * selectedQuantity).toLocaleString()}
                          </div>
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          );
        })}
      </div>

      {/* Continue Button */}
      {selectedCount > 0 && (
        <div className="continue-section mt-4 p-3 bg-light rounded">
          <Row className="align-items-center">
            <Col md={8}>
              <h6 className="mb-1">
                {selectedCount} room{selectedCount !== 1 ? 's' : ''} selected
              </h6>
              <small className="text-muted">
                Total: ৳{totalPrice.toLocaleString()} for {calculateNights()} night{calculateNights() !== 1 ? 's' : ''}
              </small>
            </Col>
            <Col md={4} className="text-end">
              <Button
                variant="primary"
                size="lg"
                onClick={handleContinue}
                disabled={!isSelectionValid()}
              >
                {isSelectionValid() ? 'Continue to Booking' : `Select ${requiredRooms - selectedCount} More`}
              </Button>
            </Col>
          </Row>
        </div>
      )}
    </div>
  );
};

export default RoomSelection;
