.seat-selection-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  );
  min-height: 100vh;
  border-radius: 20px;
}

/* Side-by-side seat map improvements */
.seat-selection-container .row .col-lg-6 .seat-map {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.seat-selection-container .card.h-100 {
  min-height: 400px;
}

/* Responsive improvements for side-by-side layout */
@media (max-width: 991.98px) {
  .seat-selection-container .row .col-lg-6 {
    margin-bottom: 1rem;
  }
}

/* Button improvements */
.seat-selection-container .btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

.seat-selection-container .row.gap-2 {
  margin-left: 0;
  margin-right: 0;
}

.seat-selection-container .row.gap-2 > .col-md-6 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Skip seat selection button styling */
.btn-outline-secondary:hover {
  background: linear-gradient(
    45deg,
    var(--primary-medium),
    var(--primary-light)
  );
  border-color: var(--primary-medium);
  color: #fff;
}

/* Seat map grid */
.seat-map {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
  position: relative;
}

.seat-row {
  display: flex;
  justify-content: center;
  gap: 10px;
  position: relative;
}

/* Add aisle space between seats C and D */
.seat-row .seat-btn:nth-child(3) {
  margin-right: 2rem;
}

/* Seat buttons */
.seat-btn {
  width: 50px;
  height: 50px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.seat-btn:not(:disabled):hover {
  transform: scale(1.1);
  z-index: 2;
}

/* Keyboard focus styles */
.seat-btn:focus-visible {
  outline: 3px solid #0d6efd;
  outline-offset: 2px;
}

/* Tooltip container */
.seat-tooltip {
  visibility: hidden;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Show tooltip on hover and focus */
.seat-btn:hover .seat-tooltip,
.seat-btn:focus .seat-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Add arrow to tooltip */
.seat-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

/* Flight direction indicator */
.flight-direction {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.flight-direction i {
  font-size: 24px;
  margin-right: 10px;
  color: #0056b3;
}

/* Cabin separator */
.cabin-separator {
  position: relative;
  text-align: center;
  padding: 10px 0;
  margin: 10px 0;
  border-bottom: 2px dashed #dee2e6;
}

.cabin-separator::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 0, 0, 0.05),
    transparent
  );
}

/* Seat type styles */
.seat-btn.standard {
  background-color: #ffffff;
  border-color: #ced4da;
}

.seat-btn.premium {
  background: #e3f2fd;
  border-color: #90caf9;
}

.seat-btn.exit {
  background: #fff3e0;
  border-color: #ffb74d;
}

.seat-btn.selected {
  background: #c8e6c9;
  border-color: #66bb6a;
}

.seat-btn.unavailable {
  background: #f5f5f5;
  border-color: #e0e0e0;
  cursor: not-allowed;
  opacity: 0.7;
}

.seat-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.seat-btn small {
  font-size: 0.75rem;
  font-weight: normal;
  margin-top: 2px;
}

/* Infant-specific seat styles */
.seat-btn.infant-seat {
  border-color: #ffc107;
  background-color: #fff3cd;
}

.seat-btn.adult-with-infant {
  border-style: dashed;
  border-color: #ffc107;
}

.seat-btn.invalid-infant-seat {
  border-color: #dc3545;
  animation: pulse-error 1s infinite;
}

@keyframes pulse-error {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* Mixed cabin warning styles */
.mixed-cabin-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-left: 4px solid #ffc107;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
}

.mixed-cabin-warning ul {
  margin-bottom: 0;
  padding-left: 1.5rem;
}

/* Seat legend enhancements */
.seat-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.seat-indicator {
  width: 24px;
  height: 24px;
  border: 2px solid;
  border-radius: 4px;
}

.seat-indicator.standard {
  background: #fff;
  border-color: #dee2e6;
}

.seat-indicator.premium {
  background: #e3f2fd;
  border-color: #90caf9;
}

.seat-indicator.exit {
  background: #fff3e0;
  border-color: #ffb74d;
}

.seat-indicator.selected {
  background: #c8e6c9;
  border-color: #66bb6a;
}

.seat-indicator.unavailable {
  background: #f5f5f5;
  border-color: #e0e0e0;
  opacity: 0.7;
}

.seat-indicator.infant-seat {
  background: #f3e5f5;
  border-color: #ce93d8;
}

/* Bassinet seat styles */
.seat-btn.bassinet {
  background-color: #ffd700;
  border-color: #ffaa00;
  color: #000;
}

.seat-btn.bassinet:hover:not(:disabled) {
  background-color: #ffaa00;
}

/* Add indicators for seat types */
.seat-btn::after {
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 8px;
}

.seat-btn.window::after {
  content: "W";
}

.seat-btn.aisle::after {
  content: "A";
}

.seat-btn.bassinet::after {
  content: "B";
}

.seat-btn.exit::after {
  content: "E";
}

/* Accessibility enhancements */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .seat-map {
    padding: 1rem;
  }

  .seat-btn {
    width: 40px;
    height: 40px;
    font-size: 12px;
  }

  .seat-tooltip {
    display: none; /* Hide tooltips on mobile */
  }

  .seat-legend {
    flex-direction: column;
    gap: 10px;
  }
}

/* High contrast mode support */
@media (forced-colors: active) {
  .seat-btn {
    border: 2px solid ButtonBorder;
  }

  .seat-btn:disabled {
    opacity: 0.5;
  }

  .seat-btn.selected {
    border: 3px solid Highlight;
  }
}

.seat-btn.suggested {
  border: 2px solid #ffd700;
  animation: pulse 2s infinite;
}

.suggestion-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  font-size: 12px;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 215, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
  }
}

.seat {
  width: 50px;
  height: 50px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.seat:not(:disabled):hover {
  transform: scale(1.1);
  z-index: 2;
}

.seat.selected {
  background-color: #e6f3ff;
  border-color: #0d6efd;
  color: #0d6efd;
}

.seat.selected-active {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: #fff;
}

.seat.unavailable {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #adb5bd;
  cursor: not-allowed;
}

.seat.premium {
  border-color: #ffc107;
}

.seat.exit-row {
  border-color: #28a745;
}

.seat.bassinet {
  border-color: #6f42c1;
}

.seat.window {
  background-color: #f8f9fa;
}

.seat.aisle {
  background-color: #fff;
}

.seat-label {
  font-size: 12px;
  font-weight: 500;
}

.seat-passenger {
  font-size: 16px;
  font-weight: bold;
  margin-top: 2px;
}
