import React, { useState } from "react";
import { Container, Row, Col, Alert } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { useAppContext } from "../context/AppContext";
import HotelSearch from "../Components/HotelSearch";
import HotelResults from "../Components/HotelResults";
import HotelFilterPanel from "../Components/HotelFilterPanel";
import HotelDetails from "../Components/HotelDetails";
import RoomSelection from "../Components/RoomSelection";
import "../assets/styles/Pages.css";

const Hotels = () => {
  const navigate = useNavigate();
  const { dispatch } = useAppContext();

  const [searchResults, setSearchResults] = useState([]);
  const [searchParams, setSearchParams] = useState(null);
  const [filteredResults, setFilteredResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [currentView, setCurrentView] = useState("search"); // search, results, room-selection
  const [selectedHotel, setSelectedHotel] = useState(null);
  const [selectedOffer, setSelectedOffer] = useState(null);
  const [showHotelDetails, setShowHotelDetails] = useState(false);

  // Handle search results
  const handleSearchResults = (hotels, params) => {
    setSearchResults(hotels);
    setFilteredResults(hotels);
    setSearchParams(params);
    setCurrentView("results");
    setError("");
  };

  // Handle filter changes
  const handleFiltersChange = (filters) => {
    let filtered = [...searchResults];

    // Apply price filter
    if (filters.priceRange) {
      filtered = filtered.filter((hotel) => {
        const price = parseFloat(hotel.offers?.[0]?.price?.total || 0);
        return (
          price >= filters.priceRange.min && price <= filters.priceRange.max
        );
      });
    }

    // Apply star rating filter
    if (filters.starRating && filters.starRating.length > 0) {
      filtered = filtered.filter((hotel) => {
        const rating = parseInt(hotel.hotel?.rating || 0);
        return filters.starRating.includes(rating);
      });
    }

    // Apply amenities filter
    if (filters.amenities && filters.amenities.length > 0) {
      filtered = filtered.filter((hotel) => {
        const hotelAmenities = hotel.hotel?.amenities || [];
        return filters.amenities.some((amenity) =>
          hotelAmenities.includes(amenity)
        );
      });
    }

    // Apply hotel chains filter
    if (filters.hotelChains && filters.hotelChains.length > 0) {
      filtered = filtered.filter((hotel) => {
        return filters.hotelChains.includes(hotel.hotel?.chainCode);
      });
    }

    setFilteredResults(filtered);
  };

  // Handle hotel selection for room selection
  const handleSelectHotel = (hotelOffer, offer) => {
    setSelectedHotel(hotelOffer);
    setSelectedOffer(offer);
    setCurrentView("room-selection");
  };

  // Handle room selection and proceed to booking
  const handleRoomSelect = (hotelOffer, selectedRooms) => {
    // Save booking data to context for the booking page
    dispatch({
      type: "SAVE_FORM_DATA",
      payload: {
        hotelBookingData: {
          hotelOffer,
          selectedRooms,
          searchParams,
        },
      },
    });

    // Navigate to hotel booking page
    navigate("/hotel-booking");
  };

  // Handle back navigation
  const handleBack = () => {
    switch (currentView) {
      case "room-selection":
        setCurrentView("results");
        setSelectedHotel(null);
        setSelectedOffer(null);
        break;
      case "results":
        setCurrentView("search");
        setSearchResults([]);
        setFilteredResults([]);
        setSearchParams(null);
        break;
      default:
        setCurrentView("search");
    }
  };

  // Handle show hotel details modal
  const handleShowDetails = (hotelOffer, offer) => {
    setSelectedHotel(hotelOffer);
    setSelectedOffer(offer);
    setShowHotelDetails(true);
  };

  // Handle book now from hotel details modal
  const handleBookNow = (hotelOffer, offer) => {
    setShowHotelDetails(false);
    handleSelectHotel(hotelOffer, offer);
  };

  console.log("Hotels component is rendering...");

  return (
    <div className="page-container">
      <Container fluid className="hotels-page">
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError("")}>
            {error}
          </Alert>
        )}

        {/* Search View */}
        {currentView === "search" && (
          <Row className="justify-content-center">
            <Col lg={8}>
              <div className="text-center mb-4">
                <h1>Find Your Perfect Hotel</h1>
                <p className="lead text-muted">
                  Discover comfortable accommodations for your next trip
                </p>
              </div>
              <HotelSearch onSearchResults={handleSearchResults} />
            </Col>
          </Row>
        )}

        {/* Results View */}
        {currentView === "results" && (
          <Row>
            <Col lg={3}>
              <HotelFilterPanel
                hotels={searchResults}
                onFiltersChange={handleFiltersChange}
              />
            </Col>
            <Col lg={9}>
              <HotelResults
                hotels={filteredResults}
                searchParams={searchParams}
                onSelectHotel={handleSelectHotel}
                isLoading={isLoading}
              />
            </Col>
          </Row>
        )}

        {/* Room Selection View */}
        {currentView === "room-selection" && selectedHotel && (
          <RoomSelection
            hotelOffer={selectedHotel}
            searchParams={searchParams}
            onRoomSelect={handleRoomSelect}
            onBack={handleBack}
          />
        )}

        {/* Hotel Details Modal */}
        <HotelDetails
          show={showHotelDetails}
          hotelOffer={selectedHotel}
          offer={selectedOffer}
          searchParams={searchParams}
          onBookNow={handleBookNow}
          onClose={() => setShowHotelDetails(false)}
        />
      </Container>
    </div>
  );
};

export default Hotels;
