import React, { useState } from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import { formatAirportDisplay } from "../../utils/airportUtils";
import { getAirlineByCode } from "../../utils/airlineUtils";
import { formatDuration, formatDateTime } from "../../utils/dateUtils";
import { PriceCalculationService } from "../../services/priceCalculationService";

const PricingSelect = ({
  selectedFlights,
  passengers,
  children = 0,
  infants = 0,
  onClose,
}) => {
  const navigate = useNavigate();
  const [selectedOption, setSelectedOption] = useState(null);

  // Get flight details for display
  const getFlightDetails = (flight) => {
    if (!flight || !flight.itineraries || flight.itineraries.length === 0)
      return null;

    const firstSegment = flight.itineraries[0].segments[0];
    const lastSegment =
      flight.itineraries[0].segments[flight.itineraries[0].segments.length - 1];
    const airline = getAirlineByCode(firstSegment.carrierCode);

    return {
      airline: airline.name || firstSegment.carrierCode,
      airlineLogo: airline.logo,
      flightNumber: `${firstSegment.carrierCode}${firstSegment.number}`,
      departureCode: firstSegment.departure.iataCode,
      departureCity: formatAirportDisplay(
        firstSegment.departure.iataCode,
        "city"
      ),
      departureCountry: formatAirportDisplay(
        firstSegment.departure.iataCode,
        "country"
      ),
      departureTime: formatDateTime(firstSegment.departure.at),
      arrivalCode: lastSegment.arrival.iataCode,
      arrivalCity: formatAirportDisplay(lastSegment.arrival.iataCode, "city"),
      arrivalCountry: formatAirportDisplay(
        lastSegment.arrival.iataCode,
        "country"
      ),
      arrivalTime: formatDateTime(lastSegment.arrival.at),
      duration: formatDuration(flight.itineraries[0].duration),
      stops: flight.itineraries[0].segments.length - 1,
      price: flight.price?.total || 0,
      currency: flight.price?.currency || "BDT",
    };
  };

  const outboundDetails = getFlightDetails(selectedFlights.outbound);
  const returnDetails = selectedFlights.return
    ? getFlightDetails(selectedFlights.return)
    : null;

  const calculatePrice = (type) => {
    const priceDetails = PriceCalculationService.calculateTotalPrice({
      flights: selectedFlights,
      passengers: {
        adult: passengers,
        child: children,
        infant: infants,
      },
      pricingOption: type,
    });

    return priceDetails.total.toFixed(2);
  };

  const proceedToBooking = (selectedOption) => {
    // Calculate final price with the selected option
    const priceDetails = PriceCalculationService.calculateTotalPrice({
      flights: selectedFlights,
      passengers: {
        adult: passengers,
        child: children,
        infant: infants,
      },
      pricingOption: selectedOption,
    });

    navigate("/booking", {
      state: {
        selectedFlights,
        pricingOption: selectedOption,
        passengers,
        children,
        infants,
        calculatedPrice: priceDetails,
      },
    });
  };

  return (
    <div className="pricing-select-container pricing-modal">
      <div className="pricing-header bg-primary text-white p-4">
        <h3 className="mb-0">Select Pricing Option</h3>
        <p className="mb-0 mt-2 opacity-75">
          Choose the fare that best suits your travel needs
        </p>
      </div>

      <div className="flight-summary p-4 border-bottom">
        <h4 className="mb-4">Your Flight Selection</h4>

        {outboundDetails && (
          <div className="flight-card mb-4 p-3 border rounded shadow-sm">
            <div className="d-flex align-items-center mb-3">
              <div className="flight-direction-badge bg-primary text-white px-3 py-1 rounded-pill me-3">
                <i className="fas fa-plane-departure me-2"></i>Outbound
              </div>
              <h5 className="mb-0">
                {outboundDetails.departureCode} → {outboundDetails.arrivalCode}
              </h5>
            </div>

            <div className="row">
              <div className="col-md-3 mb-3">
                <div className="airline-logo-container mb-2 d-flex align-items-center">
                  <img
                    src={outboundDetails.airlineLogo}
                    alt={outboundDetails.airline}
                    className="me-2"
                    style={{ maxWidth: "40px", maxHeight: "40px" }}
                    onError={(e) => {
                      e.target.src =
                        "https://cdn-icons-png.flaticon.com/512/187/187820.png";
                    }}
                  />
                  <span className="fw-bold">{outboundDetails.airline}</span>
                </div>
                <div className="text-muted small">
                  Flight {outboundDetails.flightNumber}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{outboundDetails.departureTime}</div>
                <div>
                  {outboundDetails.departureCode} -{" "}
                  {outboundDetails.departureCity}
                </div>
                <div className="text-muted small">
                  {outboundDetails.departureCountry}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{outboundDetails.arrivalTime}</div>
                <div>
                  {outboundDetails.arrivalCode} - {outboundDetails.arrivalCity}
                </div>
                <div className="text-muted small">
                  {outboundDetails.arrivalCountry}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{outboundDetails.duration}</div>
                <div>
                  {outboundDetails.stops === 0 ? (
                    <span className="text-success">Direct</span>
                  ) : (
                    <span>
                      {outboundDetails.stops}{" "}
                      {outboundDetails.stops === 1 ? "Stop" : "Stops"}
                    </span>
                  )}
                </div>
                <div className="text-success fw-bold mt-1">
                  {outboundDetails.currency} {outboundDetails.price}
                </div>
              </div>
            </div>
          </div>
        )}

        {returnDetails && (
          <div className="flight-card mb-4 p-3 border rounded shadow-sm">
            <div className="d-flex align-items-center mb-3">
              <div className="flight-direction-badge bg-secondary text-white px-3 py-1 rounded-pill me-3">
                <i className="fas fa-plane-arrival me-2"></i>Return
              </div>
              <h5 className="mb-0">
                {returnDetails.departureCode} → {returnDetails.arrivalCode}
              </h5>
            </div>

            <div className="row">
              <div className="col-md-3 mb-3">
                <div className="airline-logo-container mb-2 d-flex align-items-center">
                  <img
                    src={returnDetails.airlineLogo}
                    alt={returnDetails.airline}
                    className="me-2"
                    style={{ maxWidth: "40px", maxHeight: "40px" }}
                    onError={(e) => {
                      e.target.src =
                        "https://cdn-icons-png.flaticon.com/512/187/187820.png";
                    }}
                  />
                  <span className="fw-bold">{returnDetails.airline}</span>
                </div>
                <div className="text-muted small">
                  Flight {returnDetails.flightNumber}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{returnDetails.departureTime}</div>
                <div>
                  {returnDetails.departureCode} - {returnDetails.departureCity}
                </div>
                <div className="text-muted small">
                  {returnDetails.departureCountry}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{returnDetails.arrivalTime}</div>
                <div>
                  {returnDetails.arrivalCode} - {returnDetails.arrivalCity}
                </div>
                <div className="text-muted small">
                  {returnDetails.arrivalCountry}
                </div>
              </div>

              <div className="col-md-3 mb-3">
                <div className="fw-bold">{returnDetails.duration}</div>
                <div>
                  {returnDetails.stops === 0 ? (
                    <span className="text-success">Direct</span>
                  ) : (
                    <span>
                      {returnDetails.stops}{" "}
                      {returnDetails.stops === 1 ? "Stop" : "Stops"}
                    </span>
                  )}
                </div>
                <div className="text-success fw-bold mt-1">
                  {returnDetails.currency} {returnDetails.price}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="passenger-summary p-3 bg-light rounded">
          <h5 className="mb-3">Passenger Summary</h5>
          <div className="row">
            <div className="col-md-4">
              <div className="d-flex align-items-center mb-2">
                <i className="fas fa-user me-2"></i>
                <span>
                  Adults: <strong>{passengers}</strong>
                </span>
              </div>
            </div>
            {children > 0 && (
              <div className="col-md-4">
                <div className="d-flex align-items-center mb-2">
                  <i className="fas fa-child me-2"></i>
                  <span>
                    Children: <strong>{children}</strong>
                  </span>
                </div>
              </div>
            )}
            {infants > 0 && (
              <div className="col-md-4">
                <div className="d-flex align-items-center mb-2">
                  <i className="fas fa-baby me-2"></i>
                  <span>
                    Infants: <strong>{infants}</strong>
                  </span>
                </div>
              </div>
            )}
          </div>
          <div className="mt-2 pt-2 border-top">
            <div className="d-flex align-items-center">
              <i className="fas fa-users me-2"></i>
              <span>
                Total Passengers:{" "}
                <strong>{passengers + children + infants}</strong>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="pricing-options p-4">
        <h4 className="mb-4">Choose Your Pricing Option</h4>

        <div className="row">
          <div className="col-md-4 mb-3">
            <div
              className={`price-option h-100 ${
                selectedOption === "basic" ? "selected" : ""
              }`}
              onClick={() => setSelectedOption("basic")}
            >
              <div className="price-option-header bg-light p-3 border-bottom">
                <h5 className="mb-0">Basic</h5>
              </div>
              <div className="price-option-body p-3">
                <div className="price-amount mb-3">
                  <span className="currency">BDT</span>
                  <span className="amount fs-3 fw-bold">
                    {calculatePrice("basic")}
                  </span>
                </div>
                <ul className="feature-list list-unstyled mb-0">
                  <li className="mb-2">
                    <i className="fas fa-times-circle text-danger me-2"></i>
                    <span>Non-refundable</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-times-circle text-danger me-2"></i>
                    <span>No changes allowed</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Carry-on baggage</span>
                  </li>
                  <li>
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>In-flight meal</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="col-md-4 mb-3">
            <div
              className={`price-option h-100 ${
                selectedOption === "standard" ? "selected" : ""
              }`}
              onClick={() => setSelectedOption("standard")}
            >
              <div className="price-option-header bg-primary text-white p-3 border-bottom">
                <h5 className="mb-0">Standard</h5>
                <div className="badge bg-white text-primary mt-2">
                  Recommended
                </div>
              </div>
              <div className="price-option-body p-3">
                <div className="price-amount mb-3">
                  <span className="currency">BDT</span>
                  <span className="amount fs-3 fw-bold">
                    {calculatePrice("standard")}
                  </span>
                </div>
                <ul className="feature-list list-unstyled mb-0">
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Refundable (70%)</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Changes allowed with fee</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Carry-on baggage</span>
                  </li>
                  <li>
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>In-flight meal</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="col-md-4 mb-3">
            <div
              className={`price-option h-100 ${
                selectedOption === "premium" ? "selected" : ""
              }`}
              onClick={() => setSelectedOption("premium")}
            >
              <div className="price-option-header bg-dark text-white p-3 border-bottom">
                <h5 className="mb-0">Premium</h5>
              </div>
              <div className="price-option-body p-3">
                <div className="price-amount mb-3">
                  <span className="currency">BDT</span>
                  <span className="amount fs-3 fw-bold">
                    {calculatePrice("premium")}
                  </span>
                </div>
                <ul className="feature-list list-unstyled mb-0">
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Fully refundable</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Free changes</span>
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Extra baggage allowance</span>
                  </li>
                  <li>
                    <i className="fas fa-check-circle text-success me-2"></i>
                    <span>Priority boarding</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="actions d-flex justify-content-between p-4 border-top">
        <button onClick={onClose} className="btn btn-outline-secondary">
          <i className="fas fa-arrow-left me-2"></i>
          Back to Flight Selection
        </button>
        <button
          onClick={() => proceedToBooking(selectedOption)}
          className="btn btn-primary"
          disabled={!selectedOption}
        >
          <i className="fas fa-check-circle me-2"></i>
          Continue to Booking
        </button>
      </div>
    </div>
  );
};

PricingSelect.propTypes = {
  selectedFlights: PropTypes.shape({
    outbound: PropTypes.object.isRequired,
    return: PropTypes.object,
  }).isRequired,
  passengers: PropTypes.number.isRequired,
  children: PropTypes.number,
  infants: PropTypes.number,
  onClose: PropTypes.func.isRequired,
};

export default PricingSelect;
