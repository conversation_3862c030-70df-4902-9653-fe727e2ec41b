import React, { useState, useEffect, useRef } from 'react';
import { searchAirports } from '../utils/airportUtils';
import '../assets/styles/Autocomplete.css';

const AirportAutocomplete = ({
  name,
  value,
  onChange,
  placeholder,
  isInvalid,
  errorMessage
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState(0);
  const wrapperRef = useRef(null);

  // State for tracking API requests
  const [loading, setLoading] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState(null);

  // Search airports using local data
  const handleInputChange = (e) => {
    const userInput = e.target.value;

    // Call the parent component's onChange handler
    onChange({
      target: {
        name,
        value: userInput
      }
    });

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Clear suggestions if input is empty
    if (userInput.trim() === '') {
      setSuggestions([]);
      return;
    }

    // Debounce search to improve performance
    setSearchTimeout(
      setTimeout(() => {
        setLoading(true);
        try {
          // Use local search function instead of API
          const results = searchAirports(userInput);
          setSuggestions(results);
          setShowSuggestions(true);
        } catch (error) {
          console.error('Error searching airports:', error);
        } finally {
          setLoading(false);
        }
      }, 150) // Reduced debounce time since local search is faster
    );
  };

  // Handle suggestion selection
  const handleSuggestionClick = (airport) => {
    onChange({
      target: {
        name,
        value: airport.code
      }
    });
    setSuggestions([]);
    setShowSuggestions(false);
    setActiveSuggestionIndex(0);
  };

  const handleKeyDown = (e) => {
    // Arrow down
    if (e.keyCode === 40) {
      if (activeSuggestionIndex < suggestions.length - 1) {
        setActiveSuggestionIndex(activeSuggestionIndex + 1);
      }
    }
    // Arrow up
    else if (e.keyCode === 38) {
      if (activeSuggestionIndex > 0) {
        setActiveSuggestionIndex(activeSuggestionIndex - 1);
      }
    }
    // Enter
    else if (e.keyCode === 13) {
      e.preventDefault(); // Prevent form submission
      if (suggestions.length > 0 && showSuggestions) {
        handleSuggestionClick(suggestions[activeSuggestionIndex]);
      }
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="autocomplete-wrapper" ref={wrapperRef}>
      <input
        type="text"
        name={name}
        className={`form-control ${isInvalid ? 'is-invalid' : ''}`}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          setShowSuggestions(true);
        }}
        onBlur={() => {}}
        placeholder={placeholder}
        autoComplete="off"
        required
      />
      {isInvalid && (
        <div className="invalid-feedback">{errorMessage}</div>
      )}

      {showSuggestions && (
        <ul className="autocomplete-suggestions">
          {loading ? (
            <li className="suggestion-item loading">
              <div className="spinner-border spinner-border-sm text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <span className="ms-2">Searching...</span>
            </li>
          ) : suggestions.length > 0 ? (
            suggestions.map((location, index) => (
              <li
                key={`${location.code}-${index}`} // Use a combination of code and index for unique keys
                onClick={() => handleSuggestionClick(location)}
                className={`suggestion-item ${
                  index === activeSuggestionIndex ? "active" : ""
                }`}
              >
                <div className="suggestion-header">
                  <span className="airport-code">{location.code}</span>
                  <span className="location-type">{location.type === 'AIRPORT' ? 'Airport' : 'City'}</span>
                </div>
                <span className="airport-name">{location.name}</span>
                <span className="airport-location">{location.city}{location.country ? `, ${location.country}` : ''}</span>
              </li>
            ))
          ) : value.trim() !== '' && (
            <li className="suggestion-item no-results">
              No matching airports or cities found
            </li>
          )}
        </ul>
      )}
    </div>
  );
};

export default AirportAutocomplete;
