.booking-confirmation {
  margin-bottom: 2rem;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  );
  min-height: 100vh;
  padding: 20px;
  border-radius: 20px;
}

.booking-reference {
  font-size: 1.2rem;
  font-weight: bold;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  padding: 0.75rem 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(255, 183, 3, 0.3);
}

.flight-path {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.path-line {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 50%;
  background: linear-gradient(
    180deg,
    var(--primary-medium),
    var(--primary-light)
  );
  z-index: 0;
}

.duration {
  background: linear-gradient(
    45deg,
    var(--primary-light),
    rgba(142, 202, 230, 0.3)
  );
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-weight: bold;
  color: var(--primary-dark);
  margin-bottom: 0.5rem;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(2, 48, 71, 0.1);
}

.stops {
  background-color: white;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  color: var(--medium-gray);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  box-shadow: 0 4px 10px rgba(2, 48, 71, 0.1);
  z-index: 1;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: bold;
  color: #198754;
}

.pnr-copy-button {
  transition: all 0.3s ease;
  margin-top: 10px;
}

.pnr-copy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pnr-copy-button.copied {
  background-color: #198754;
  border-color: #198754;
}

.pnr-display {
  background-color: #f8fff8 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid #198754;
  margin: 2rem auto;
  padding: 2rem;
}

.pnr-display:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.pnr-code {
  font-family: "Courier New", monospace;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  user-select: all;
  background-color: #f0f8ff;
  padding: 15px 30px;
  border-radius: 5px;
  border: 1px dashed #0d6efd;
  min-width: 200px;
  margin: 15px 0;
  color: #0d6efd;
  letter-spacing: 2px;
  color: #0d6efd;
  background-color: #f0f8ff;
  padding: 10px;
  border-radius: 5px;
  border: 1px dashed #0d6efd;
  display: inline-block;
  min-width: 200px;
}

.price-breakdown .table {
  margin-bottom: 0;
}

.price-breakdown .table td {
  padding: 0.5rem;
  vertical-align: middle;
}

.price-breakdown .border-top td {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
}

.price-breakdown .border-2 td {
  border-top-width: 2px;
}

.promotional-discount {
  color: #28a745;
  font-weight: 600;
}

.promo-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #e8f5e9;
  border: 1px solid #28a745;
  color: #28a745;
  border-radius: 4px;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

.promo-invalid {
  background-color: #ffebee;
  border-color: #dc3545;
  color: #dc3545;
}

/* Print styles */
@media print {
  .booking-confirmation {
    max-width: 100%;
    padding: 20px;
    margin: 0 auto;
  }

  .pnr-display {
    page-break-inside: avoid;
    margin: 20px auto;
    border: 2px solid #198754;
    padding: 20px;
    text-align: center;
  }

  .pnr-code {
    font-family: monospace;
    font-size: 24pt;
    font-weight: bold;
    color: #000;
    margin: 10px 0;
  }

  .flight-details,
  .passenger-details,
  .payment-details {
    page-break-inside: avoid;
    margin: 20px 0;
  }

  .card {
    border: 1px solid #ddd;
    page-break-inside: avoid;
  }

  .no-print,
  .actions,
  .booking-actions {
    display: none !important;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  table,
  th,
  td {
    border: 1px solid #ddd;
  }

  th,
  td {
    padding: 8px;
    text-align: left;
  }

  .badge {
    border: 1px solid #000;
    padding: 2px 6px;
  }
}
