.pnr-lookup-container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.pnr-lookup-container .card {
  border-radius: 8px;
  overflow: hidden;
}

.pnr-lookup-container .card-header {
  padding: 1.25rem;
}

.pnr-lookup-container input {
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  text-transform: uppercase;
  font-weight: bold;
}

.pnr-lookup-container .form-control-lg {
  font-size: 1.5rem;
  padding: 0.75rem 1rem;
}

.pnr-lookup-container .btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

/* Animation for loading state */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.pnr-lookup-container .btn:disabled {
  animation: pulse 1.5s infinite;
}
