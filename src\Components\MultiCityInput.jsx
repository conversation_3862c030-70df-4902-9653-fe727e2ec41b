import React from "react";
import AirportAutocomplete from "./AirportAutocomplete";
import DatePicker from "./DatePicker";
import "../assets/styles/MultiCity.css";

const MultiCityInput = ({
  segments,
  onChange,
  onAddSegment,
  onRemoveSegment,
  errors,
}) => {
  const handleSegmentChange = (index, field, value) => {
    const updatedSegments = [...segments];
    updatedSegments[index] = {
      ...updatedSegments[index],
      [field]: value,
    };
    onChange(updatedSegments);
  };

  return (
    <div className="multi-city-container">
      {segments.map((segment, index) => (
        <div key={index} className="multi-city-segment mb-4">
          <div className="segment-header">
            <h5>
              <i className="fas fa-plane-departure"></i>
              Flight {index + 1}
            </h5>
            {segments.length > 2 && (
              <button
                type="button"
                className="btn btn-outline-danger segment-remove-btn"
                onClick={() => onRemoveSegment(index)}
                aria-label={`Remove flight ${index + 1}`}
              >
                <i className="fas fa-times"></i>
                Remove Flight
              </button>
            )}
          </div>

          <div className="row g-3 align-items-end">
            <div className="col-md-3">
              <div className="field-group">
                <label className="form-label fw-semibold">
                  Origin (IATA Code)
                  <span className="visually-hidden">
                    Select departure city or airport
                  </span>
                </label>
                <AirportAutocomplete
                  name={`origin-${index}`}
                  value={segment.origin}
                  onChange={(e) =>
                    handleSegmentChange(index, "origin", e.target.value)
                  }
                  placeholder="From"
                  isInvalid={errors && errors[index]?.origin}
                  errorMessage={errors && errors[index]?.origin}
                />
              </div>
            </div>

            <div className="col-md-auto d-flex align-items-center">
              <div className="arrow-icon">
                <i className="fas fa-arrow-right"></i>
              </div>
            </div>

            <div className="col-md-3">
              <div className="field-group">
                <label className="form-label fw-semibold">
                  Destination (IATA Code)
                  <span className="visually-hidden">
                    Select arrival city or airport
                  </span>
                </label>
                <AirportAutocomplete
                  name={`destination-${index}`}
                  value={segment.destination}
                  onChange={(e) =>
                    handleSegmentChange(index, "destination", e.target.value)
                  }
                  placeholder="To"
                  isInvalid={errors && errors[index]?.destination}
                  errorMessage={errors && errors[index]?.destination}
                />
              </div>
            </div>

            <div className="col-md-auto d-flex align-items-center">
              <div className="date-separator">
                <i className="fas fa-calendar-alt"></i>
              </div>
            </div>

            <div className="col-md-4">
              <div className="field-group">
                <DatePicker
                  name={`date-${index}`}
                  selected={segment.date}
                  onChange={(e) =>
                    handleSegmentChange(index, "date", e.target.value)
                  }
                  label="Departure Date*"
                  minDate={new Date()}
                  isInvalid={errors && errors[index]?.date}
                  errorMessage={errors && errors[index]?.date}
                  placeholder="Select date"
                  required={true}
                />
              </div>
            </div>
          </div>
        </div>
      ))}

      {segments.length < 5 && (
        <div className="text-center">
          <button
            type="button"
            className="add-segment-button"
            onClick={onAddSegment}
          >
            <i className="fas fa-plus-circle"></i>
            Add Another Flight
          </button>
        </div>
      )}
    </div>
  );
};

export default MultiCityInput;
