import airportsData from '../data/airports.json';

/**
 * Get airport information by IATA code
 * @param {string} iataCode - The IATA code to look up
 * @returns {Object} - Airport information or default object if not found
 */
export const getAirportByCode = (iataCode) => {
  if (!iataCode) return { name: 'Unknown', city: 'Unknown', country: 'Unknown' };

  const airport = airportsData.airports.find(
    airport => airport.code === iataCode
  );

  return airport || {
    name: String(iataCode || 'Unknown'), // Ensure name is a string
    city: String(iataCode || 'Unknown'), // Ensure city is a string
    country: ''
  };
};

/**
 * Format airport display text
 * @param {string} iataCode - The IATA code
 * @param {string} format - Display format ('city', 'country', 'full', or 'code')
 * @returns {string} - Formatted airport text
 */
export const formatAirportDisplay = (iataCode, format = 'city') => {
  const airport = getAirportByCode(iataCode);

  switch (format) {
    case 'city':
      return airport.city;
    case 'country':
      return airport.country;
    case 'full':
      return `${airport.city}, ${airport.country}`;
    case 'name':
      return airport.name;
    case 'code':
    default:
      return iataCode;
  }
};

/**
 * Search airports from local data based on a search term
 * @param {string} searchTerm - The search term to look for
 * @param {number} limit - Maximum number of results to return
 * @returns {Array} - Array of matching airport objects
 */
export const searchAirports = (searchTerm, limit = 10) => {
  if (!searchTerm || searchTerm.length < 1) {
    return [];
  }

  // Convert search term to lowercase for case-insensitive matching
  const term = searchTerm.toLowerCase();

  // Search in code, name, city, and country fields
  const results = airportsData.airports.filter(airport =>
    airport.code.toLowerCase().includes(term) ||
    airport.name.toLowerCase().includes(term) ||
    airport.city.toLowerCase().includes(term) ||
    airport.country.toLowerCase().includes(term)
  );

  // Format results to match the structure expected by the autocomplete component
  return results.slice(0, limit).map(airport => ({
    id: airport.code,
    code: airport.code,
    name: airport.name,
    city: airport.city,
    country: airport.country,
    type: 'AIRPORT'
  }));
};
