import { request } from "./apiClient";

/**
 * Search for flights using Amadeus API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Flight offers
 */
export const searchFlights = async (params) => {
  try {
    const response = await request({
      url: "/v2/shopping/flight-offers",
      method: "get",
      params,
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Flight search failed:", error);
    throw error;
  }
};

/**
 * Create a flight booking
 * @param {Object} flightOffers - Selected flight offers
 * @param {Array} travelers - Passenger details
 * @returns {Promise<Object>} Booking confirmation with PNR
 */
export const createBooking = async (flightOffers, travelers) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      // Skip pricing verification in development
      console.log("Development mode - skipping pricing verification");
      const mockPNR = `PNR-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
      const mockOrderId = `DEV-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;

      return {
        data: {
          type: "flight-order",
          id: mockOrderId,
          queuingOfficeId: "NCE4D31FC",
          associatedRecords: [
            {
              reference: mockPNR,
              creationDate: new Date().toISOString(),
              originSystemCode: "AMADEUS",
              flightOfferId: flightOffers[0].id,
            },
          ],
          travelers: travelers.map((traveler, index) => ({
            ...traveler,
            id: `TR${index + 1}`,
            dateOfBirth: traveler.dateOfBirth || "1990-01-15",
          })),
          flightOffers: flightOffers.map((offer) => ({
            ...offer,
            id: offer.id || "1",
          })),
        },
        pnr: mockPNR,
        status: "CONFIRMED",
      };
    }

    // Production mode
    const pricingResponse = await request({
      url: "/v1/shopping/flight-offers/pricing",
      method: "post",
      data: {
        data: {
          type: "flight-offers-pricing",
          flightOffers: [].concat(flightOffers), // Ensure array
          include: ["detailed-fare-rules", "other-services"],
        },
      },
    });

    const verifiedFlightOffers =
      pricingResponse.data.data?.flightOffers || flightOffers;

    // Step 2: Create the booking
    const bookingResponse = await request({
      url: "/v1/booking/flight-orders",
      method: "post",
      data: {
        data: {
          type: "flight-order",
          flightOffers: verifiedFlightOffers,
          travelers,
        },
      },
    });

    // If seat selections are present, add them to the booking
    if (flightOffers.some((offer) => offer.seatSelections)) {
      const seatAssignments = [];

      flightOffers.forEach((offer, flightIndex) => {
        if (offer.seatSelections) {
          Object.entries(offer.seatSelections).forEach(
            ([seat, passengerId]) => {
              seatAssignments.push({
                traveler: passengerId,
                segmentId: offer.id,
                seat: seat,
              });
            }
          );
        }
      });

      if (seatAssignments.length > 0) {
        await request({
          url: `/v1/booking/flight-orders/${bookingResponse.data.data.id}/seats`,
          method: "post",
          data: {
            seatAssignments: seatAssignments,
          },
        });
      }
    }

    // Extract PNR from response
    const bookingData = bookingResponse.data.data;
    const pnr =
      bookingData.associatedRecords?.[0]?.reference ||
      bookingData.id ||
      "PNR-" + Math.random().toString(36).substring(2, 10).toUpperCase();

    // Return enhanced booking data with easily accessible PNR and required fields
    const enhancedResponse = {
      ...bookingResponse.data,
      pnr,
      status: bookingData.status || "CONFIRMED",
      creationDate: new Date().toISOString(),
      data: {
        ...bookingData, // Use bookingData directly
        associatedRecords: [
          {
            reference: pnr,
            creationDate: new Date().toISOString(),
            originSystemCode: "AMADEUS",
          },
        ],
        travelers,
        flightOffers,
        ticketingAgreement: {
          option: "DELAY_TO_CANCEL", // Defaulting as pricingOption is not available here
          delay: "PT24H",
        },
      },
    };

    return enhancedResponse;
  } catch (error) {
    console.error("Booking creation failed:", error);
    throw error;
  }
};

/**
 * Format passenger data for API request
 * @param {Array} passengers - Passenger details from form
 * @returns {Array} Formatted travelers for API
 */
export const formatTravelers = (passengers) => {
  let adultCount = 0;

  return passengers.map((passenger, index) => {
    // Base traveler object with common fields
    const traveler = {
      id: `TR${index + 1}`,
      dateOfBirth: passenger.dob,
      name: {
        firstName: passenger.firstName,
        lastName: passenger.lastName,
      },
      gender: passenger.gender || "MALE", // Default to MALE if not specified
      contact: {
        emailAddress: passenger.email || "<EMAIL>",
        phones: [
          {
            deviceType: "MOBILE",
            countryCallingCode: "880",
            number: passenger.phone || "1234567890",
          },
        ],
      },
      documents: [
        {
          documentType: "PASSPORT",
          birthPlace: passenger.birthPlace || "Dhaka",
          issuanceLocation: passenger.issuanceLocation || "Dhaka",
          issuanceDate: passenger.issuanceDate || "2015-04-14",
          number: passenger.passport,
          expiryDate: passenger.passportExpiry || "2025-04-14",
          issuanceCountry: "BD",
          validityCountry: "BD",
          nationality: "BD",
          holder: true,
        },
      ],
    };

    // Handle passenger types and infant associations
    if (passenger.type) {
      traveler.travelerType = passenger.type;

      if (passenger.type === "ADULT") {
        adultCount++;
      } else if (passenger.type === "INFANT") {
        // Associate infant with an adult
        // If associatedAdult is not specified, assign to the first available adult
        const associatedAdultId =
          passenger.associatedAdult || Math.ceil(adultCount / 2).toString();
        traveler.associatedAdultId = associatedAdultId;

        // Add infant-specific fields
        if (passenger.dob) {
          // Validate infant age (must be under 2 years)
          const birthDate = new Date(passenger.dob);
          const today = new Date();
          const ageInMonths =
            (today - birthDate) / (1000 * 60 * 60 * 24 * 30.44);

          if (ageInMonths > 24) {
            throw new Error(
              `Passenger ${passenger.firstName} ${passenger.lastName} is over 24 months old and cannot be booked as an infant`
            );
          }
        }

        // Add special service request for infant
        traveler.remarks = [
          {
            subType: "GENERAL_MISCELLANEOUS",
            text: "INFANT IN CABIN",
          },
        ];
      }
    }

    return traveler;
  });
};

/**
 * Retrieve booking details by PNR (Passenger Name Record)
 * @param {string} pnr - The PNR/booking reference to retrieve
 * @returns {Promise<Object>} Booking details
 */
export const getBookingByPNR = async (pnr) => {
  if (!pnr) {
    throw new Error("PNR is required");
  }

  try {
    // Use the Flight Order Management API to retrieve booking details
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}`,
      method: "get",
    });

    // Return the booking data with enhanced PNR information
    return {
      ...response.data,
      pnr: pnr,
    };
  } catch (error) {
    console.error("Booking retrieval failed:", error);
    throw error;
  }
};

/**
 * Search for flights with flexible dates using Amadeus API
 * @param {Object} baseParams - Base search parameters
 * @param {number} flexDays - Number of days to search before and after
 * @returns {Promise<Array>} Flight offers for different dates
 */
export const searchFlexibleDates = async (baseParams, flexDays) => {
  const results = [];
  const dates = [];

  // Ensure currency is set to BDT
  baseParams.currencyCode = "BDT";

  // Generate array of dates to search
  const baseDate = new Date(baseParams.departureDate);
  for (let i = -flexDays; i <= flexDays; i++) {
    const date = new Date(baseDate);
    date.setDate(date.getDate() + i);
    dates.push(date.toISOString().split("T")[0]);
  }

  try {
    // Search flights for each date sequentially
    for (const date of dates) {
      const params = {
        ...baseParams,
        departureDate: date,
      };

      try {
        const response = await request({
          url: "/v2/shopping/flight-offers",
          method: "get",
          params,
        });

        if (response.data?.data) {
          // Get the lowest price for this date
          const lowestPrice = response.data.data.reduce((min, offer) => {
            const price = parseFloat(offer.price.total);
            return price < min ? price : min;
          }, Infinity);

          results.push({
            date: date,
            flights: response.data.data,
            lowestPrice: lowestPrice === Infinity ? null : lowestPrice,
          });
        }
      } catch (error) {
        console.error(`Error fetching flights for date ${date}:`, error);
        // Add empty result to maintain date sequence
        results.push({
          date: date,
          flights: [],
          lowestPrice: null,
          error:
            error.response?.status === 429
              ? "Rate limit exceeded"
              : "Search failed",
        });
      }
    }

    return results;
  } catch (error) {
    console.error("Flexible date search failed:", error);
    throw error;
  }
};

/**
 * Process payment for flight booking
 * @param {Object} paymentDetails - Payment information
 * @param {number} amount - Total amount to charge
 * @returns {Promise<Object>} Payment confirmation
 */
export const processPayment = async (paymentDetails, amount) => {
  try {
    // Production mode
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      // Development mode - simulate payment processing
      console.log("Development mode - simulating payment processing:", {
        paymentDetails,
        amount,
      });

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Always return success in development
      return {
        success: true,
        transactionId: `DEV-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        timestamp: new Date().toISOString(),
        amount,
        currency: "BDT",
        status: "COMPLETED",
      };
    }

    // Production mode
    const paymentGatewayUrl = import.meta.env.VITE_PAYMENT_GATEWAY_URL;
    const merchantId = import.meta.env.VITE_MERCHANT_ID;
    const apiKey = import.meta.env.VITE_PAYMENT_API_KEY;

    if (!paymentGatewayUrl || !merchantId || !apiKey) {
      throw new Error(
        "Payment gateway configuration is missing in production mode."
      );
    }

    // Only attempt API call if we have all configuration
    const paymentData = {
      merchantId,
      amount,
      currency: "BDT",
      ...formatPaymentData(paymentDetails),
      timestamp: new Date().toISOString(),
    };

    const response = await request({
      url: paymentGatewayUrl,
      method: "post",
      data: paymentData,
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
    });

    return response.data;
  } catch (error) {
    console.error("Payment processing failed:", error);
    throw error;
  }
};

/**
 * Format payment details based on payment method
 * @param {Object} details - Raw payment details
 * @returns {Object} Formatted payment data
 */
const formatPaymentData = (details) => {
  switch (details.method) {
    case "card":
      return {
        paymentMethod: "CREDIT_CARD",
        cardNumber: details.cardNumber.replace(/\s/g, ""),
        cardHolder: details.cardHolder,
        expiryDate: details.expiryDate,
        cvv: details.cvv,
      };

    case "bkash":
    case "rocket":
      return {
        paymentMethod: details.method.toUpperCase(),
        phoneNumber: details.phoneNumber,
        transactionId: details.transactionId,
      };

    case "bank":
      return {
        paymentMethod: "BANK_TRANSFER",
        senderName: details.senderName,
        senderAccount: details.senderAccount,
        transferDate: details.transferDate,
        referenceNumber: details.referenceNumber,
      };

    case "paypal":
      return {
        paymentMethod: "PAYPAL",
        // PayPal specific fields would be handled by their SDK
        orderId: details.orderId,
      };

    default:
      throw new Error("Unsupported payment method");
  }
};

/**
 * Verify payment status
 * @param {string} transactionId - Payment transaction ID
 * @returns {Promise<Object>} Payment status
 */
export const verifyPayment = async (transactionId) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // In development, always return confirmed status for transaction IDs starting with DEV-
      if (transactionId.startsWith("DEV-")) {
        return {
          status: "CONFIRMED",
          transactionId,
          timestamp: new Date().toISOString(),
          message: "Payment verified successfully",
        };
      }
    }

    // Production mode verification
    const paymentGatewayUrl = import.meta.env.VITE_PAYMENT_GATEWAY_URL;
    const apiKey = import.meta.env.VITE_PAYMENT_API_KEY;

    if (!paymentGatewayUrl || !apiKey) {
      throw new Error(
        "Payment gateway configuration is missing in production mode."
      );
    }

    const response = await request({
      url: `${paymentGatewayUrl}/verify/${transactionId}`,
      method: "get",
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });

    return response.data;
  } catch (error) {
    console.error("Payment verification failed:", error);
    throw error;
  }
};

// ==================== HOTEL BOOKING SERVICES ====================

/**
 * Search for hotels by city using Amadeus Hotel Search API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers
 */
export const searchHotelsByCity = async (params) => {
  try {
    const response = await request({
      url: "/v1/reference-data/locations/hotels/by-city",
      method: "get",
      params: {
        cityCode: params.cityCode,
        radius: params.radius || 5,
        radiusUnit: params.radiusUnit || "KM",
        hotelSource: params.hotelSource || "ALL",
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel search by city failed:", error);
    throw error;
  }
};

/**
 * Search for hotels by geocode using Amadeus Hotel Search API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers
 */
export const searchHotelsByGeocode = async (params) => {
  try {
    const response = await request({
      url: "/v1/reference-data/locations/hotels/by-geocode",
      method: "get",
      params: {
        latitude: params.latitude,
        longitude: params.longitude,
        radius: params.radius || 5,
        radiusUnit: params.radiusUnit || "KM",
        hotelSource: params.hotelSource || "ALL",
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel search by geocode failed:", error);
    throw error;
  }
};

/**
 * Search for hotel offers using Amadeus Hotel Shopping API
 * @param {Object} params - Search parameters
 * @returns {Promise<Array>} Hotel offers with pricing
 */
export const searchHotelOffers = async (params) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock hotel offers");
      return generateMockHotelOffers(params);
    }

    const response = await request({
      url: "/v3/shopping/hotel-offers",
      method: "get",
      params: {
        hotelIds: params.hotelIds,
        adults: params.adults || 1,
        checkInDate: params.checkInDate,
        checkOutDate: params.checkOutDate,
        roomQuantity: params.roomQuantity || 1,
        currency: params.currency || "BDT",
        lang: params.lang || "EN",
        includeClosed: params.includeClosed || false,
        bestRateOnly: params.bestRateOnly || true,
      },
    });
    return response.data?.data || [];
  } catch (error) {
    console.error("Hotel offers search failed:", error);
    throw error;
  }
};

/**
 * Get hotel details by hotel ID
 * @param {string} hotelId - Hotel ID
 * @returns {Promise<Object>} Hotel details
 */
export const getHotelDetails = async (hotelId) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - returning mock hotel details");
      return generateMockHotelDetails(hotelId);
    }

    const response = await request({
      url: `/v1/reference-data/locations/hotels/${hotelId}`,
      method: "get",
    });
    return response.data?.data || {};
  } catch (error) {
    console.error("Hotel details fetch failed:", error);
    throw error;
  }
};

/**
 * Create a hotel booking using Amadeus Hotel Booking API
 * @param {Object} hotelOffer - Selected hotel offer
 * @param {Array} guests - Guest details
 * @param {Object} payments - Payment information
 * @returns {Promise<Object>} Booking confirmation
 */
export const createHotelBooking = async (hotelOffer, guests, payments) => {
  try {
    // Check if we're in development mode
    if (
      process.env.NODE_ENV === "development" ||
      import.meta.env.MODE === "development"
    ) {
      console.log("Development mode - creating mock hotel booking");
      const mockConfirmationNumber = `HTL-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

      return {
        data: {
          type: "hotel-booking",
          id: `DEV-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
          providerConfirmationId: mockConfirmationNumber,
          associatedRecords: [
            {
              reference: mockConfirmationNumber,
              originSystemCode: "AMADEUS",
            },
          ],
          guests: guests.map((guest, index) => ({
            ...guest,
            id: `GT${index + 1}`,
          })),
          hotelOffer,
        },
        confirmationNumber: mockConfirmationNumber,
        status: "CONFIRMED",
      };
    }

    // Production mode
    const response = await request({
      url: "/v1/booking/hotel-bookings",
      method: "post",
      data: {
        data: {
          type: "hotel-booking",
          hotelOffer,
          guests,
          payments,
        },
      },
    });

    const bookingData = response.data.data;
    const confirmationNumber =
      bookingData.providerConfirmationId ||
      bookingData.associatedRecords?.[0]?.reference ||
      `HTL-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;

    return {
      ...response.data,
      confirmationNumber,
      status: bookingData.bookingStatus || "CONFIRMED",
    };
  } catch (error) {
    console.error("Hotel booking creation failed:", error);
    throw error;
  }
};

/**
 * Format guest data for hotel booking API request
 * @param {Array} guests - Guest details from form
 * @returns {Array} Formatted guests for API
 */
export const formatHotelGuests = (guests) => {
  return guests.map((guest, index) => ({
    id: `GT${index + 1}`,
    name: {
      title: guest.title || "MR",
      firstName: guest.firstName,
      lastName: guest.lastName,
    },
    contact: {
      phone: guest.phone || "+8801234567890",
      email: guest.email || "<EMAIL>",
    },
    dateOfBirth: guest.dateOfBirth,
    ...(guest.specialRequests && { specialRequests: guest.specialRequests }),
  }));
};

/**
 * Generate mock hotel offers for development
 * @param {Object} params - Search parameters
 * @returns {Array} Mock hotel offers
 */
const generateMockHotelOffers = (params) => {
  const mockHotels = [
    {
      type: "hotel-offers",
      hotel: {
        type: "hotel",
        hotelId: "DHCOXBAZ",
        chainCode: "RT",
        dupeId: "700001234",
        name: "Cox's Bazar Beach Resort",
        rating: "4",
        cityCode: "CXB",
        latitude: 21.4272,
        longitude: 92.0058,
        hotelDistance: {
          distance: 0.5,
          distanceUnit: "KM",
        },
        address: {
          lines: ["Beach Road"],
          postalCode: "4700",
          cityName: "Cox's Bazar",
          countryCode: "BD",
        },
        contact: {
          phone: "+880-341-64001",
          fax: "+880-341-64002",
          email: "<EMAIL>",
        },
        amenities: [
          "SWIMMING_POOL",
          "RESTAURANT",
          "WIFI",
          "SPA",
          "FITNESS_CENTER",
        ],
      },
      available: true,
      offers: [
        {
          id: "OFFER1234567890",
          checkInDate: params.checkInDate,
          checkOutDate: params.checkOutDate,
          rateCode: "RAC",
          rateFamilyEstimated: {
            code: "SRS",
            type: "P",
          },
          room: {
            type: "A2K",
            typeEstimated: {
              category: "SUPERIOR_ROOM",
              beds: 1,
              bedType: "KING",
            },
            description: {
              text: "Superior King Room with Sea View",
            },
          },
          guests: {
            adults: params.adults || 1,
          },
          price: {
            currency: "BDT",
            base: "8500.00",
            total: "9350.00",
            taxes: [
              {
                code: "VAT",
                amount: "850.00",
                currency: "BDT",
                included: false,
              },
            ],
          },
          policies: {
            paymentType: "guarantee",
            cancellation: {
              deadline: params.checkInDate,
              amount: "8500.00",
              type: "FULL_STAY",
            },
          },
          self: "https://test.api.amadeus.com/v3/shopping/hotel-offers/OFFER1234567890",
        },
      ],
      self: "https://test.api.amadeus.com/v3/shopping/hotel-offers?hotelIds=DHCOXBAZ",
    },
  ];

  return mockHotels;
};

/**
 * Generate mock hotel details for development
 * @param {string} hotelId - Hotel ID
 * @returns {Object} Mock hotel details
 */
const generateMockHotelDetails = (hotelId) => {
  return {
    type: "location",
    subType: "HOTEL",
    id: hotelId,
    self: {
      href: `https://test.api.amadeus.com/v1/reference-data/locations/hotels/${hotelId}`,
      methods: ["GET"],
    },
    name: "Cox's Bazar Beach Resort",
    iataCode: "CXB",
    address: {
      cityName: "Cox's Bazar",
      countryCode: "BD",
    },
    geoCode: {
      latitude: 21.4272,
      longitude: 92.0058,
    },
    distance: {
      value: 0.5,
      unit: "KM",
    },
    lastUpdate: new Date().toISOString(),
  };
};

/**
 * Modify passenger information in a booking
 * @param {string} pnr - The booking reference
 * @param {Array} travelers - Updated traveler information
 * @returns {Promise<Object>} Updated booking details
 */
export const modifyBookingPassengers = async (pnr, travelers) => {
  try {
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}`,
      method: "patch",
      data: {
        data: {
          type: "flight-order",
          travelers,
        },
      },
    });
    return response.data;
  } catch (error) {
    console.error("Failed to modify passenger information:", error);
    throw error;
  }
};

/**
 * Modify seat assignments in a booking
 * @param {string} pnr - The booking reference
 * @param {Array} seatAssignments - New seat assignments
 * @returns {Promise<Object>} Updated booking details
 */
export const modifyBookingSeats = async (pnr, seatAssignments) => {
  try {
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}/seats`,
      method: "post",
      data: {
        seatAssignments: seatAssignments,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Failed to modify seat assignments:", error);
    throw error;
  }
};

/**
 * Cancel a booking
 * @param {string} pnr - The booking reference
 * @returns {Promise<Object>} Cancellation confirmation
 */
export const cancelBooking = async (pnr) => {
  try {
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}`,
      method: "delete",
    });
    return response.data;
  } catch (error) {
    console.error("Failed to cancel booking:", error);
    throw error;
  }
};

/**
 * Calculate change fees and price difference for a booking modification
 * @param {string} pnr - The booking reference
 * @param {Object} changes - Proposed changes
 * @returns {Promise<Object>} Price breakdown and fees
 */
export const calculateModificationFees = async (pnr, changes) => {
  try {
    const response = await request({
      url: `/v1/booking/flight-orders/${encodeURIComponent(pnr)}/modification-options`,
      method: "post",
      data: {
        changes,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Modification fees calculation failed:", error);
    throw error;
  }
};
