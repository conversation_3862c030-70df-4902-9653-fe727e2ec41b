/* Modern Homepage Design with Custom Color Palette */
:root {
  --primary-light: #8ecae6; /* Light blue */
  --primary-medium: #219ebc; /* Medium blue */
  --primary-dark: #023047; /* Dark blue */
  --accent-yellow: #ffb703; /* Yellow */
  --accent-orange: #fb8500; /* Orange */

  /* Additional colors for better design */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;
  --shadow: rgba(2, 48, 71, 0.1);
  --shadow-hover: rgba(2, 48, 71, 0.2);
}

/* Global Overrides for Modern Design */
.home-container {
  overflow-x: hidden;
}

/* Modern Hero Section */
.hero-section {
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary-medium) 50%,
    var(--primary-light) 100%
  );
  min-height: calc(100vh - 80px); /* Subtract navbar height */
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  margin-top: 0px;
  /* Account for fixed navbar */
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  z-index: 1;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-element {
  position: absolute;
  opacity: 0.3;
  animation-duration: 20s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  font-size: 3rem;
  color: var(--accent-yellow);
  text-shadow: 0 0 20px rgba(255, 183, 3, 0.5);
}

.floating-element.plane {
  top: 15%;
  left: 10%;
  animation-name: float-diagonal;
  animation-delay: 0s;
}

.floating-element.hotel {
  top: 70%;
  right: 15%;
  animation-name: float-reverse;
  animation-delay: 7s;
}

.floating-element.palm {
  bottom: 20%;
  left: 20%;
  animation-name: float-vertical;
  animation-delay: 14s;
}

.floating-element.compass {
  top: 40%;
  right: 25%;
  animation-name: float-diagonal;
  animation-delay: 5s;
}

.floating-element.luggage {
  bottom: 40%;
  right: 10%;
  animation-name: float-reverse;
  animation-delay: 10s;
}

.floating-element.camera {
  top: 25%;
  left: 70%;
  animation-name: float-vertical;
  animation-delay: 18s;
}

@keyframes float-diagonal {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(50px, -30px) rotate(5deg);
  }
  50% {
    transform: translate(100px, -60px) rotate(0deg);
  }
  75% {
    transform: translate(50px, -30px) rotate(-5deg);
  }
}

@keyframes float-reverse {
  0%,
  100% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(-80px, -40px) scale(1.1);
  }
}

@keyframes float-vertical {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-80px) rotate(10deg);
  }
}

.hero-content {
  max-width: 1200px;
  padding: 2rem 2rem;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--white);
  /* border: 5px solid red; */
  margin-top: 250px;
}

.hero-badge {
  display: inline-block;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(255, 183, 3, 0.3);
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 4px 15px rgba(255, 183, 3, 0.3);
  }
  50% {
    box-shadow: 0 6px 25px rgba(255, 183, 3, 0.5);
  }
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-gradient {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.hero-content p {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.hero-buttons .btn {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 183, 3, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 183, 3, 0.6);
  color: var(--primary-dark);
}

.btn-outline {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.btn-outline:hover {
  background: var(--white);
  color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--accent-yellow);
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Fade-in Animation */
.fade-in {
  animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section Styling */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  border-radius: 2px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--medium-gray);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Services Section */
.services-section {
  padding: 6rem 0;
  background: var(--light-gray);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(142, 202, 230, 0.2);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    45deg,
    var(--primary-medium),
    var(--primary-light)
  );
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow-hover);
}

.service-header {
  margin-bottom: 1.5rem;
}

.service-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  background: linear-gradient(
    45deg,
    var(--primary-medium),
    var(--primary-light)
  );
  box-shadow: 0 8px 20px rgba(33, 158, 188, 0.3);
}

.service-icon-container i {
  font-size: 2rem;
  color: var(--white);
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1rem;
}

.service-card p {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.service-features {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.service-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: var(--medium-gray);
  font-size: 0.9rem;
}

.service-features i {
  color: var(--accent-yellow);
  font-size: 0.8rem;
}

.service-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.service-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 183, 3, 0.4);
  color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    margin-top: 70px; /* Slightly less margin for mobile */
    min-height: calc(100vh - 70px);
  }

  .hero-content {
    padding: 1rem 1rem;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-buttons .btn {
    min-width: 250px;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .section-header h2 {
    font-size: 2.2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .floating-element {
    font-size: 2rem;
  }
}

/* Featured Destinations Section */
.featured-section {
  padding: 6rem 0;
  background: var(--white);
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.destination-card {
  background: var(--white);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px var(--shadow);
  border: 1px solid rgba(142, 202, 230, 0.2);
}

.destination-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px var(--shadow-hover);
}

.destination-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.destination-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.destination-card:hover .destination-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3));
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 1.5rem;
}

.price-tag {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 10px rgba(255, 183, 3, 0.3);
}

.rating-badge {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-dark);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.rating-badge i {
  color: var(--accent-yellow);
}

.destination-content {
  padding: 2rem;
}

.destination-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 0.5rem;
}

.destination-content p {
  color: var(--medium-gray);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.destination-activities {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-medium);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.destination-activities i {
  color: var(--accent-orange);
}

.destination-btn {
  background: linear-gradient(
    45deg,
    var(--primary-medium),
    var(--primary-light)
  );
  color: var(--white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.destination-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(33, 158, 188, 0.4);
  color: var(--white);
}

/* Why Choose Us Section */
.why-choose-section {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  );
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: var(--white);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.feature-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(142, 202, 230, 0.3);
}

.feature-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  box-shadow: 0 8px 20px rgba(255, 183, 3, 0.3);
}

.feature-icon-container i {
  font-size: 2rem;
  color: var(--primary-dark);
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 1rem;
}

.feature-item p {
  color: var(--medium-gray);
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
  padding: 6rem 0;
  background: var(--white);
}

.testimonial-carousel {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial-card {
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary-medium) 100%
  );
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 8rem;
  color: rgba(255, 255, 255, 0.1);
  font-family: serif;
  line-height: 1;
}

.testimonial-content {
  position: relative;
  z-index: 2;
}

.testimonial-rating {
  margin-bottom: 1.5rem;
}

.testimonial-rating i {
  color: var(--accent-yellow);
  font-size: 1.2rem;
  margin: 0 0.2rem;
}

.testimonial-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid var(--accent-yellow);
  object-fit: cover;
}

.author-info {
  text-align: left;
}

.author-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.3rem;
}

.author-location {
  font-size: 0.9rem;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.testimonial-navigation {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: var(--medium-gray);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: var(--accent-yellow);
  transform: scale(1.2);
}

/* Newsletter Section */
.newsletter-section {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary-medium) 100%
  );
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.newsletter-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E");
  z-index: 1;
}

.newsletter-content {
  text-align: center;
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  box-shadow: 0 8px 20px rgba(255, 183, 3, 0.3);
}

.newsletter-icon i {
  font-size: 2rem;
  color: var(--primary-dark);
}

.newsletter-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--white);
}

.newsletter-content p {
  font-size: 1.1rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.newsletter-form {
  margin-bottom: 2rem;
}

.form-group-inline {
  display: flex;
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.form-group-inline input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-dark);
  min-width: 250px;
}

.form-group-inline input::placeholder {
  color: var(--medium-gray);
}

.form-group-inline input:focus {
  outline: none;
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(255, 183, 3, 0.3);
}

.form-group-inline button {
  padding: 1rem 2rem;
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  color: var(--primary-dark);
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.form-group-inline button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 183, 3, 0.4);
}

.newsletter-benefits {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.benefit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.benefit i {
  color: var(--accent-yellow);
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--accent-yellow) 0%,
    var(--accent-orange) 100%
  );
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c16.569 0 30-13.431 30-30H30v30z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  z-index: 1;
}

.cta-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-dark);
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-content p {
  font-size: 1.2rem;
  color: var(--primary-dark);
  margin-bottom: 2.5rem;
  opacity: 0.8;
  line-height: 1.6;
}

.cta-btn {
  background: var(--primary-dark);
  color: var(--white);
  padding: 1.2rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 8px 25px rgba(2, 48, 71, 0.3);
}

.cta-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(2, 48, 71, 0.4);
  color: var(--white);
}

/* Additional Responsive Design */
@media (max-width: 768px) {
  .destinations-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .form-group-inline {
    flex-direction: column;
  }

  .form-group-inline input {
    min-width: 100%;
  }

  .newsletter-benefits {
    flex-direction: column;
    gap: 1rem;
  }

  .newsletter-content h2 {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 2.2rem;
  }

  .testimonial-author {
    flex-direction: column;
    text-align: center;
  }

  .author-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .destination-card {
    margin: 0 1rem;
  }

  .service-card,
  .feature-item {
    margin: 0 1rem;
  }

  .testimonial-card {
    margin: 0 1rem;
    padding: 2rem;
  }
}
