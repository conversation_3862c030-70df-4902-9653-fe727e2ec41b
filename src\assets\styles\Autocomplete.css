.autocomplete-wrapper {
  position: relative;
  width: 100%;
}

.autocomplete-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 300px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  z-index: 1000;
  padding: 0;
  margin: 0;
  list-style: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item.active {
  background-color: #e9ecef;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.airport-code {
  font-weight: bold;
  font-size: 1rem;
  color: #0d6efd;
}

.location-type {
  font-size: 0.75rem;
  color: #fff;
  background-color: #6c757d;
  padding: 2px 6px;
  border-radius: 4px;
}

.location-type.airport {
  background-color: #0d6efd;
}

.location-type.city {
  background-color: #198754;
}

.airport-name {
  font-size: 0.9rem;
  color: #212529;
  margin-bottom: 2px;
}

.airport-location {
  font-size: 0.8rem;
  color: #6c757d;
}

.suggestion-item.loading {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  padding: 15px;
}

.suggestion-item.no-results {
  color: #6c757d;
  text-align: center;
  font-style: italic;
}
