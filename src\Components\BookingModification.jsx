import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  modifyBookingPassengers,
  modifyBookingSeats,
  cancelBooking,
  calculateModificationFees
} from '../services/apiService';
import PassengerModification from './PassengerModification';
import SeatModification from './SeatModification';
import '../assets/styles/BookingModification.css';

const BookingModification = ({ booking, onModificationComplete }) => {
  const [modType, setModType] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [modificationFees, setModificationFees] = useState(null);
  const navigate = useNavigate();

  const handleModificationSelect = async (type) => {
    setModType(type);
    setError(null);

    if (type === 'cancel') {
      handleCancellation();
      return;
    }

    // Calculate fees for the selected modification type
    try {
      const fees = await calculateModificationFees(booking.pnr, { type });
      setModificationFees(fees.data);
    } catch (err) {
      setError('Failed to calculate modification fees. Please try again.');
    }
  };

  const handlePassengerModification = async (updatedPassengers) => {
    setLoading(true);
    setError(null);

    try {
      const result = await modifyBookingPassengers(booking.pnr, updatedPassengers);
      onModificationComplete(result);
    } catch (err) {
      setError('Failed to update passenger information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSeatModification = async (newSeatAssignments) => {
    setLoading(true);
    setError(null);

    try {
      const result = await modifyBookingSeats(booking.pnr, newSeatAssignments);
      onModificationComplete(result);
    } catch (err) {
      setError('Failed to update seat assignments. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancellation = async () => {
    if (!window.confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await cancelBooking(booking.pnr);
      if (result.success) {
        alert('Booking cancelled successfully');
        navigate('/');
      }
    } catch (err) {
      setError('Failed to cancel booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="booking-modification">
      <h4 className="mb-4">Modify Booking</h4>
      
      {/* Modification Type Selection */}      {!modType && (
        <div className="modification-options">
          <div className="row g-3">
            <div className="col-md-4">
              <div 
                className="card h-100 cursor-pointer"
                onClick={() => handleModificationSelect('passenger')}
              >
                <div className="card-body text-center">
                  <i className="fas fa-users fa-2x mb-3 text-primary"></i>
                  <h5 className="card-title">Update Passenger Info</h5>
                  <p className="card-text small">Modify passenger details and documentation</p>
                </div>
              </div>
            </div>
            
            <div className="col-md-4">
              <div 
                className="card h-100 cursor-pointer"
                onClick={() => handleModificationSelect('seats')}
              >
                <div className="card-body text-center">
                  <i className="fas fa-chair fa-2x mb-3 text-primary"></i>
                  <h5 className="card-title">Change Seats</h5>
                  <p className="card-text small">Select different seats for your flights</p>
                </div>
              </div>
            </div>
            
            <div className="col-md-4">
              <div 
                className="card h-100 cursor-pointer border-danger"
                onClick={() => handleModificationSelect('cancel')}
              >
                <div className="card-body text-center">
                  <i className="fas fa-times-circle fa-2x mb-3 text-danger"></i>
                  <h5 className="card-title text-danger">Cancel Booking</h5>
                  <p className="card-text small">Cancel this booking (fees may apply)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Passenger Modification Form */}
      {modType === 'passenger' && !loading && (
        <PassengerModification
          passengers={booking.passengerData}
          onSubmit={handlePassengerModification}
          onCancel={() => {
            setModType(null);
            setModificationFees(null);
          }}
        />
      )}

      {/* Seat Modification Component */}
      {modType === 'seats' && !loading && (
        <SeatModification
          booking={booking}
          onSubmit={handleSeatModification}
          onCancel={() => {
            setModType(null);
            setModificationFees(null);
          }}
        />
      )}

      {/* Modification Fees Display */}
      {modificationFees && (
        <div className="alert alert-info mt-4">
          <h6 className="alert-heading">Modification Fees</h6>
          <p className="mb-0">
            Change Fee: {modificationFees.changeFees.amount} {modificationFees.changeFees.currency}
            <br />
            Price Difference: {modificationFees.priceDifference.amount} {modificationFees.priceDifference.currency}
          </p>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="alert alert-danger mt-4">
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </div>
      )}

      {/* Loading Indicator */}
      {loading && (
        <div className="text-center mt-4">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Processing your request...</p>
        </div>
      )}

      {/* Cancel Button */}
      {modType && !loading && (
        <button
          className="btn btn-secondary mt-4"
          onClick={() => {
            setModType(null);
            setModificationFees(null);
          }}
        >
          Back to Options
        </button>
      )}
    </div>
  );
};

export default BookingModification;
