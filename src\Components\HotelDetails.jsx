import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Badge, Modal, Tab, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { FaStar, FaMapMarkerAlt, FaWifi, FaSwimmingPool, FaUtensils, FaSpa, FaDumbbell, FaBed, FaUsers, FaPhone, FaEnvelope, FaCheck, FaTimes } from 'react-icons/fa';
import { getHotelDetails } from '../services/hotelService';

const HotelDetails = ({ hotelOffer, offer, searchParams, onBookNow, onClose, show }) => {
  const [hotelDetails, setHotelDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  const hotel = hotelOffer?.hotel;

  // Fetch detailed hotel information
  useEffect(() => {
    if (show && hotel?.hotelId) {
      fetchHotelDetails();
    }
  }, [show, hotel?.hotelId]);

  const fetchHotelDetails = async () => {
    setLoading(true);
    setError('');
    try {
      const details = await getHotelDetails(hotel.hotelId);
      setHotelDetails(details);
    } catch (err) {
      console.error('Failed to fetch hotel details:', err);
      setError('Failed to load hotel details');
    } finally {
      setLoading(false);
    }
  };

  // Amenity icons mapping
  const amenityIcons = {
    WIFI: { icon: <FaWifi />, label: 'Free WiFi' },
    SWIMMING_POOL: { icon: <FaSwimmingPool />, label: 'Swimming Pool' },
    RESTAURANT: { icon: <FaUtensils />, label: 'Restaurant' },
    SPA: { icon: <FaSpa />, label: 'Spa' },
    FITNESS_CENTER: { icon: <FaDumbbell />, label: 'Fitness Center' },
    BUSINESS_CENTER: { icon: <FaBed />, label: 'Business Center' },
    CONCIERGE: { icon: <FaUsers />, label: 'Concierge' },
    ROOM_SERVICE: { icon: <FaUtensils />, label: 'Room Service' },
    PARKING: { icon: <FaMapMarkerAlt />, label: 'Parking' },
    AIR_CONDITIONING: { icon: <FaCheck />, label: 'Air Conditioning' },
  };

  // Render star rating
  const renderStars = (rating) => {
    const stars = [];
    const numRating = parseInt(rating || 0);
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <FaStar
          key={i}
          className={i <= numRating ? 'text-warning' : 'text-muted'}
        />
      );
    }
    return stars;
  };

  // Format price
  const formatPrice = (price) => {
    if (!price) return 'Price not available';
    return `৳${parseFloat(price.total).toLocaleString()} ${price.currency}`;
  };

  // Calculate total nights
  const calculateNights = () => {
    if (searchParams?.checkInDate && searchParams?.checkOutDate) {
      const checkIn = new Date(searchParams.checkInDate);
      const checkOut = new Date(searchParams.checkOutDate);
      const diffTime = checkOut - checkIn;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    }
    return 1;
  };

  // Get cancellation policy text
  const getCancellationPolicyText = (policy) => {
    if (!policy) return 'Cancellation policy not available';
    
    if (policy.type === 'FREE_CANCELLATION') {
      return 'Free cancellation until check-in date';
    } else if (policy.type === 'FULL_STAY') {
      return `Cancellation fee: ${policy.amount} ${offer?.price?.currency || 'BDT'}`;
    }
    return 'Standard cancellation policy applies';
  };

  if (!hotel) {
    return null;
  }

  return (
    <Modal show={show} onHide={onClose} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <div className="d-flex align-items-center">
            <div>
              <h4 className="mb-0">{hotel.name}</h4>
              <div className="d-flex align-items-center mt-1">
                <div className="star-rating me-2">
                  {renderStars(hotel.rating)}
                </div>
                <small className="text-muted">
                  <FaMapMarkerAlt className="me-1" />
                  {hotel.address?.cityName}, {hotel.address?.countryCode}
                </small>
              </div>
            </div>
          </div>
        </Modal.Title>
      </Modal.Header>

      <Modal.Body className="p-0">
        {loading ? (
          <div className="text-center py-5">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3">Loading hotel details...</p>
          </div>
        ) : error ? (
          <Alert variant="danger" className="m-3">
            {error}
          </Alert>
        ) : (
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="px-3 pt-3">
            {/* Overview Tab */}
            <Tab eventKey="overview" title="Overview">
              <div className="p-3">
                <Row>
                  {/* Hotel Image Placeholder */}
                  <Col md={6}>
                    <div 
                      className="hotel-image bg-light d-flex align-items-center justify-content-center rounded mb-3"
                      style={{ height: '300px', backgroundColor: '#f8f9fa' }}
                    >
                      <FaBed size={60} className="text-muted" />
                    </div>
                  </Col>

                  {/* Hotel Information */}
                  <Col md={6}>
                    <div className="hotel-info">
                      <h5>Hotel Information</h5>
                      
                      {/* Address */}
                      <div className="mb-3">
                        <h6>Address</h6>
                        <p className="text-muted mb-1">
                          {hotel.address?.lines?.join(', ')}
                        </p>
                        <p className="text-muted mb-1">
                          {hotel.address?.cityName}, {hotel.address?.postalCode}
                        </p>
                        <p className="text-muted">
                          {hotel.address?.countryCode}
                        </p>
                        {hotel.hotelDistance && (
                          <small className="text-muted">
                            {hotel.hotelDistance.distance} {hotel.hotelDistance.distanceUnit} from city center
                          </small>
                        )}
                      </div>

                      {/* Contact Information */}
                      {hotel.contact && (
                        <div className="mb-3">
                          <h6>Contact</h6>
                          {hotel.contact.phone && (
                            <p className="mb-1">
                              <FaPhone className="me-2" />
                              {hotel.contact.phone}
                            </p>
                          )}
                          {hotel.contact.email && (
                            <p className="mb-1">
                              <FaEnvelope className="me-2" />
                              {hotel.contact.email}
                            </p>
                          )}
                        </div>
                      )}

                      {/* Chain Information */}
                      {hotel.chainCode && (
                        <div className="mb-3">
                          <h6>Hotel Chain</h6>
                          <Badge bg="secondary">{hotel.chainCode}</Badge>
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>
              </div>
            </Tab>

            {/* Room Details Tab */}
            <Tab eventKey="room" title="Room Details">
              <div className="p-3">
                {offer ? (
                  <Row>
                    <Col md={8}>
                      <Card className="mb-3">
                        <Card.Header>
                          <h5 className="mb-0">Selected Room</h5>
                        </Card.Header>
                        <Card.Body>
                          <h6>{offer.room?.description?.text || 'Room'}</h6>
                          
                          <Row className="mb-3">
                            <Col md={6}>
                              <strong>Room Type:</strong>
                              <p className="mb-1">{offer.room?.typeEstimated?.category?.replace('_', ' ') || 'Standard Room'}</p>
                            </Col>
                            <Col md={6}>
                              <strong>Bed Type:</strong>
                              <p className="mb-1">{offer.room?.typeEstimated?.bedType || 'Standard'}</p>
                            </Col>
                          </Row>

                          <Row className="mb-3">
                            <Col md={6}>
                              <strong>Number of Beds:</strong>
                              <p className="mb-1">{offer.room?.typeEstimated?.beds || 1}</p>
                            </Col>
                            <Col md={6}>
                              <strong>Guests:</strong>
                              <p className="mb-1">
                                <FaUsers className="me-1" />
                                {offer.guests?.adults || searchParams?.adults} Adult{(offer.guests?.adults || searchParams?.adults) !== 1 ? 's' : ''}
                              </p>
                            </Col>
                          </Row>

                          {/* Check-in/Check-out */}
                          <Row className="mb-3">
                            <Col md={6}>
                              <strong>Check-in:</strong>
                              <p className="mb-1">{offer.checkInDate}</p>
                            </Col>
                            <Col md={6}>
                              <strong>Check-out:</strong>
                              <p className="mb-1">{offer.checkOutDate}</p>
                            </Col>
                          </Row>

                          <div className="mb-3">
                            <strong>Duration:</strong>
                            <p className="mb-1">{calculateNights()} night{calculateNights() !== 1 ? 's' : ''}</p>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>

                    <Col md={4}>
                      {/* Price Breakdown */}
                      <Card>
                        <Card.Header>
                          <h6 className="mb-0">Price Breakdown</h6>
                        </Card.Header>
                        <Card.Body>
                          <div className="d-flex justify-content-between mb-2">
                            <span>Base Price:</span>
                            <span>৳{parseFloat(offer.price?.base || 0).toLocaleString()}</span>
                          </div>
                          
                          {offer.price?.taxes && offer.price.taxes.map((tax, index) => (
                            <div key={index} className="d-flex justify-content-between mb-2">
                              <span>{tax.code}:</span>
                              <span>৳{parseFloat(tax.amount).toLocaleString()}</span>
                            </div>
                          ))}
                          
                          <hr />
                          <div className="d-flex justify-content-between">
                            <strong>Total:</strong>
                            <strong className="text-primary">
                              {formatPrice(offer.price)}
                            </strong>
                          </div>
                          
                          <small className="text-muted d-block mt-2">
                            For {searchParams?.roomQuantity || 1} room{(searchParams?.roomQuantity || 1) !== 1 ? 's' : ''} × {calculateNights()} night{calculateNights() !== 1 ? 's' : ''}
                          </small>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>
                ) : (
                  <Alert variant="info">
                    No room details available
                  </Alert>
                )}
              </div>
            </Tab>

            {/* Amenities Tab */}
            <Tab eventKey="amenities" title="Amenities">
              <div className="p-3">
                <h5>Hotel Amenities</h5>
                {hotel.amenities && hotel.amenities.length > 0 ? (
                  <Row>
                    {hotel.amenities.map((amenity, index) => {
                      const amenityInfo = amenityIcons[amenity] || { 
                        icon: <FaCheck />, 
                        label: amenity.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) 
                      };
                      
                      return (
                        <Col md={6} lg={4} key={index} className="mb-3">
                          <div className="d-flex align-items-center">
                            <span className="amenity-icon me-2 text-primary">
                              {amenityInfo.icon}
                            </span>
                            <span>{amenityInfo.label}</span>
                          </div>
                        </Col>
                      );
                    })}
                  </Row>
                ) : (
                  <Alert variant="info">
                    No amenities information available
                  </Alert>
                )}
              </div>
            </Tab>

            {/* Policies Tab */}
            <Tab eventKey="policies" title="Policies">
              <div className="p-3">
                <h5>Booking Policies</h5>
                
                {offer?.policies ? (
                  <div>
                    {/* Cancellation Policy */}
                    <Card className="mb-3">
                      <Card.Header>
                        <h6 className="mb-0">Cancellation Policy</h6>
                      </Card.Header>
                      <Card.Body>
                        <p>{getCancellationPolicyText(offer.policies.cancellation)}</p>
                        {offer.policies.cancellation?.deadline && (
                          <small className="text-muted">
                            Deadline: {offer.policies.cancellation.deadline}
                          </small>
                        )}
                      </Card.Body>
                    </Card>

                    {/* Payment Policy */}
                    <Card className="mb-3">
                      <Card.Header>
                        <h6 className="mb-0">Payment Policy</h6>
                      </Card.Header>
                      <Card.Body>
                        <p>
                          Payment Type: {offer.policies.paymentType || 'Standard'}
                        </p>
                        <small className="text-muted">
                          Payment is required to guarantee your reservation
                        </small>
                      </Card.Body>
                    </Card>
                  </div>
                ) : (
                  <Alert variant="info">
                    No policy information available
                  </Alert>
                )}
              </div>
            </Tab>
          </Tabs>
        )}
      </Modal.Body>

      <Modal.Footer>
        <div className="d-flex justify-content-between align-items-center w-100">
          <div>
            {offer?.price && (
              <div>
                <h5 className="text-primary mb-0">
                  {formatPrice(offer.price)}
                </h5>
                <small className="text-muted">
                  Total for {calculateNights()} night{calculateNights() !== 1 ? 's' : ''}
                </small>
              </div>
            )}
          </div>
          <div>
            <Button variant="secondary" onClick={onClose} className="me-2">
              Close
            </Button>
            <Button 
              variant="primary" 
              size="lg"
              onClick={() => onBookNow && onBookNow(hotelOffer, offer)}
            >
              Book Now
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default HotelDetails;
