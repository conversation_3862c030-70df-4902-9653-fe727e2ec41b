import React, { useState } from 'react';
import '../assets/styles/FilterPanel.css';

const FilterPanel = ({ onFilterChange, airlines }) => {
  const [filters, setFilters] = useState({
    airlines: [],
    stops: [],
    departureTime: [],
    arrivalTime: [],
    duration: { min: '', max: '' }
  });

  // Time ranges for departure/arrival filters
  const timeRanges = [
    { id: 'morning', label: 'Morning (6am - 12pm)', start: '06:00', end: '11:59' },
    { id: 'afternoon', label: 'Afternoon (12pm - 6pm)', start: '12:00', end: '17:59' },
    { id: 'evening', label: 'Evening (6pm - 12pm)', start: '18:00', end: '23:59' },
    { id: 'night', label: 'Night (12am - 6am)', start: '00:00', end: '05:59' }
  ];

  // Stop options
  const stopOptions = [
    { id: 'nonstop', label: 'Non-stop', value: 0 },
    { id: 'oneStop', label: '1 Stop', value: 1 },
    { id: 'twoStops', label: '2+ Stops', value: 2 }
  ];

  // Handle checkbox changes
  const handleCheckboxChange = (category, value) => {
    const updatedFilters = { ...filters };
    
    if (updatedFilters[category].includes(value)) {
      // Remove value if already selected
      updatedFilters[category] = updatedFilters[category].filter(item => item !== value);
    } else {
      // Add value if not selected
      updatedFilters[category] = [...updatedFilters[category], value];
    }
    
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  // Handle duration input changes
  const handleDurationChange = (field, value) => {
    const updatedFilters = {
      ...filters,
      duration: {
        ...filters.duration,
        [field]: value
      }
    };
    
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  // Reset all filters
  const handleResetFilters = () => {
    const resetFilters = {
      airlines: [],
      stops: [],
      departureTime: [],
      arrivalTime: [],
      duration: { min: '', max: '' }
    };
    
    setFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="filter-panel">
      <div className="filter-header">
        <h5>Filter Options</h5>
        <button 
          className="btn btn-sm btn-outline-secondary" 
          onClick={handleResetFilters}
        >
          Reset
        </button>
      </div>
      
      {/* Airlines Filter */}
      {airlines && airlines.length > 0 && (
        <div className="filter-section">
          <h6>Airlines</h6>
          <div className="filter-options">
            {airlines.map(airline => (
              <div className="form-check" key={airline.code}>
                <input
                  className="form-check-input"
                  type="checkbox"
                  id={`airline-${airline.code}`}
                  checked={filters.airlines.includes(airline.code)}
                  onChange={() => handleCheckboxChange('airlines', airline.code)}
                />
                <label className="form-check-label" htmlFor={`airline-${airline.code}`}>
                  {airline.name}
                </label>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Stops Filter */}
      <div className="filter-section">
        <h6>Stop Count</h6>
        <div className="filter-options">
          {stopOptions.map(option => (
            <div className="form-check" key={option.id}>
              <input
                className="form-check-input"
                type="checkbox"
                id={option.id}
                checked={filters.stops.includes(option.value)}
                onChange={() => handleCheckboxChange('stops', option.value)}
              />
              <label className="form-check-label" htmlFor={option.id}>
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>
      
      {/* Departure Time Filter */}
      <div className="filter-section">
        <h6>Departure Time</h6>
        <div className="filter-options">
          {timeRanges.map(range => (
            <div className="form-check" key={`dep-${range.id}`}>
              <input
                className="form-check-input"
                type="checkbox"
                id={`dep-${range.id}`}
                checked={filters.departureTime.includes(range.id)}
                onChange={() => handleCheckboxChange('departureTime', range.id)}
              />
              <label className="form-check-label" htmlFor={`dep-${range.id}`}>
                {range.label}
              </label>
            </div>
          ))}
        </div>
      </div>
      
      {/* Arrival Time Filter */}
      <div className="filter-section">
        <h6>Arrival Time</h6>
        <div className="filter-options">
          {timeRanges.map(range => (
            <div className="form-check" key={`arr-${range.id}`}>
              <input
                className="form-check-input"
                type="checkbox"
                id={`arr-${range.id}`}
                checked={filters.arrivalTime.includes(range.id)}
                onChange={() => handleCheckboxChange('arrivalTime', range.id)}
              />
              <label className="form-check-label" htmlFor={`arr-${range.id}`}>
                {range.label}
              </label>
            </div>
          ))}
        </div>
      </div>
      
      {/* Duration Filter */}
      <div className="filter-section">
        <h6>Flight Duration (Minutes)</h6>
        <div className="duration-inputs">
          <div className="input-group input-group-sm mb-2">
            <span className="input-group-text">Minimum</span>
            <input
              type="number"
              className="form-control"
              value={filters.duration.min}
              onChange={(e) => handleDurationChange('min', e.target.value)}
              placeholder="Minutes"
              min="0"
            />
          </div>
          <div className="input-group input-group-sm">
            <span className="input-group-text">Maximum</span>
            <input
              type="number"
              className="form-control"
              value={filters.duration.max}
              onChange={(e) => handleDurationChange('max', e.target.value)}
              placeholder="Minutes"
              min="0"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
