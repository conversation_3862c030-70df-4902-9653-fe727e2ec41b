/* Hero Section - Modern Update */
.hero-section {
  background:
    linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4)),
    url("/images/bangladesh-hero.jpg") center/cover;
  min-height: calc(100vh - 80px); /* Subtract navbar height */
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  margin-top: 80px; /* Account for fixed navbar */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-element {
  position: absolute;
  opacity: 0.7;
  animation-duration: 15s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  font-size: 2.5rem;
}

.floating-element.plane {
  top: 20%;
  left: 10%;
  animation-name: float-right;
}

.floating-element.hotel {
  top: 60%;
  right: 15%;
  animation-name: float-left;
}

.floating-element.palm {
  bottom: 15%;
  left: 20%;
  animation-name: float-up;
}

@keyframes float-right {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(100px) translateY(-30px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes float-left {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(-100px) translateY(-20px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes float-up {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-50px);
  }
  100% {
    transform: translateY(0);
  }
}

.hero-content {
  max-width: 1200px;
  padding: 2rem 2rem;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 0.75rem 2rem;
  border-radius: 50px;
  margin-bottom: 2rem;
  font-weight: 500;
  font-size: 1.1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform: translateY(0);
  transition: all 0.3s ease;
}

.hero-badge:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.hero-content h1 {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  margin-bottom: 1.5rem;
  font-weight: 800;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.text-gradient {
  background: linear-gradient(90deg, #ffb938, #ff5757);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.hero-content p {
  font-size: clamp(1.1rem, 2vw, 1.8rem);
  margin-bottom: 3rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Modern Button Styles */
.btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn i {
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: translateX(3px);
}

.btn-primary {
  background: linear-gradient(90deg, #ff5757, #ffb938);
  border: none;
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 87, 87, 0.2);
}

.btn-outline {
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Stats Section */
.hero-stats {
  display: flex;
  justify-content: center;
  gap: clamp(2rem, 4vw, 4rem);
  margin-top: 3rem;
  flex-wrap: wrap;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 700;
  background: linear-gradient(90deg, #ffb938, #ff5757);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Section Containers */
.section-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, #2d3250, #424769);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Services Section */
.services-section {
  padding-top: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.service-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-card:hover::before {
  opacity: 1;
}

.service-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.service-icon-container {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon-container {
  transform: rotate(10deg);
}

.service-header h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2d3250;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.service-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  color: #666;
}

.service-features li i {
  color: #4caf50;
  font-size: 0.9rem;
}

.service-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary);
  font-weight: 600;
  text-decoration: none;
  transition: gap 0.3s ease;
}

.service-btn:hover {
  gap: 1rem;
}

/* Destinations Section */
.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  margin-top: 2rem;
}

.destination-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.destination-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.destination-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.destination-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.destination-card:hover .destination-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 1.5rem;
  color: white;
}

.price-tag {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 600;
}

.rating-badge {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 600;
}

.rating-badge i {
  color: #ffd700;
}

.destination-content {
  padding: 1.5rem;
}

.destination-content h3 {
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  color: #2d3250;
}

.destination-activities {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  margin: 1rem 0;
  font-size: 0.9rem;
}

.destination-activities i {
  color: var(--primary);
}

.destination-btn {
  width: 100%;
  text-align: center;
  margin-top: 1rem;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.destination-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Testimonials Section */
.testimonials-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  position: relative;
  overflow: hidden;
}

.testimonial-carousel {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.testimonial-card {
  background: white;
  border-radius: 30px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 8rem;
  color: #ffb938;
  opacity: 0.1;
  font-family: serif;
}

.testimonial-rating {
  margin-bottom: 1.5rem;
}

.testimonial-rating i {
  color: #ffd700;
  font-size: 1.2rem;
  margin: 0 0.2rem;
}

.testimonial-content p {
  font-size: 1.2rem;
  color: #444;
  line-height: 1.8;
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ffb938;
}

.author-info {
  text-align: left;
}

.author-name {
  font-weight: 600;
  color: #2d3250;
  margin-bottom: 0.2rem;
}

.author-location {
  color: #666;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.testimonial-navigation {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: #ffb938;
  transform: scale(1.2);
}

/* Newsletter Section */
.newsletter-section {
  background: linear-gradient(135deg, #2d3250, #424769);
  color: white;
  position: relative;
  overflow: hidden;
}

.newsletter-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("/images/pattern.png") repeat;
  opacity: 0.1;
}

.newsletter-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.newsletter-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  font-size: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.newsletter-content h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.newsletter-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.newsletter-form {
  margin-bottom: 2rem;
}

.form-group-inline {
  display: flex;
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.form-group-inline input {
  flex: 1;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  border: none;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group-inline input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-group-inline .btn {
  white-space: nowrap;
}

.newsletter-benefits {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.benefit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.benefit i {
  color: #ffb938;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #ff5757, #ffb938);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-btn {
  background: white;
  color: #ff5757;
  border: none;
}

.cta-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
