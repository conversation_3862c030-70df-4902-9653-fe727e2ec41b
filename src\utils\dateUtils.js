/**
 * Utility functions for date and time formatting
 */

/**
 * Format duration string from ISO 8601 duration format
 * @param {string} durationStr - ISO 8601 duration string (e.g., "PT2H30M")
 * @returns {string} Formatted duration (e.g., "2h 30m")
 */
export const formatDuration = (durationStr) => {
  if (!durationStr) return "N/A";
  
  // Parse PT2H30M format
  const hours = durationStr.match(/(\d+)H/);
  const minutes = durationStr.match(/(\d+)M/);
  
  let result = "";
  if (hours) result += `${hours[1]}h `;
  if (minutes) result += `${minutes[1]}m`;
  
  return result.trim();
};

/**
 * Format date and time from ISO string
 * @param {string} dateTimeStr - ISO date string
 * @returns {string} Formatted date and time (e.g., "10:30 AM, 15 Jun")
 */
export const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return "N/A";
  
  const date = new Date(dateTimeStr);
  
  // Format time (10:30 AM)
  const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true };
  const timeStr = date.toLocaleTimeString('en-US', timeOptions);
  
  // Format date (15 Jun)
  const dateOptions = { day: 'numeric', month: 'short' };
  const dateStr = date.toLocaleDateString('en-US', dateOptions);
  
  return `${timeStr}, ${dateStr}`;
};

/**
 * Format date only from ISO string
 * @param {string} dateStr - ISO date string
 * @returns {string} Formatted date (e.g., "15 Jun 2023")
 */
export const formatDate = (dateStr) => {
  if (!dateStr) return "N/A";
  
  const date = new Date(dateStr);
  const options = { day: 'numeric', month: 'short', year: 'numeric' };
  
  return date.toLocaleDateString('en-US', options);
};

/**
 * Format time only from ISO string
 * @param {string} timeStr - ISO date string
 * @returns {string} Formatted time (e.g., "10:30 AM")
 */
export const formatTime = (timeStr) => {
  if (!timeStr) return "N/A";
  
  const date = new Date(timeStr);
  const options = { hour: '2-digit', minute: '2-digit', hour12: true };
  
  return date.toLocaleTimeString('en-US', options);
};

/**
 * Calculate and format the time difference between two ISO date strings
 * @param {string} startTimeStr - ISO date string for start time
 * @param {string} endTimeStr - ISO date string for end time
 * @returns {string} Formatted duration (e.g., "2h 30m")
 */
export const calculateDuration = (startTimeStr, endTimeStr) => {
  if (!startTimeStr || !endTimeStr) return "N/A";
  
  const startTime = new Date(startTimeStr);
  const endTime = new Date(endTimeStr);
  
  // Calculate difference in milliseconds
  const diffMs = endTime - startTime;
  
  // Convert to hours and minutes
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  let result = "";
  if (hours > 0) result += `${hours}h `;
  if (minutes > 0) result += `${minutes}m`;
  
  return result.trim();
};
