/**
 * Format duration string from ISO 8601 format
 * @param {string} duration - Duration in ISO 8601 format (e.g., PT2H30M)
 * @returns {string} - Formatted duration (e.g., 2h 30m)
 */
export const formatDuration = (duration) => {
  if (!duration) return "N/A";
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?/);
  if (!match) return "N/A";
  
  const hours = match[1] ? `${match[1]}h ` : "";
  const minutes = match[2] ? `${match[2]}m` : "";
  return hours + minutes || "N/A";
};

/**
 * Format price with currency symbol and localization
 * @param {number|string} price - Price value
 * @param {string} currency - Currency code (e.g., USD, EUR)
 * @returns {string} - Formatted price with currency symbol
 */
export const formatPrice = (price, currency = "BDT") => {
  if (!price) return "N/A";
  
  const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(numericPrice);
  } catch (error) {
    // Fallback formatting if Intl.NumberFormat fails
    const currencySymbols = {
      USD: "$",
      EUR: "€",
      GBP: "£",
      BDT: "৳",
      INR: "₹",
      JPY: "¥",
    };
    
    const symbol = currencySymbols[currency] || currency;
    return `${symbol}${numericPrice.toFixed(0)}`;
  }
};

/**
 * Format date to display in a user-friendly format
 * @param {string|Date} date - Date to format
 * @param {object} options - Intl.DateTimeFormat options
 * @returns {string} - Formatted date string
 */
export const formatDate = (date, options = {}) => {
  if (!date) return "N/A";
  
  const defaultOptions = {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    ...options
  };
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', defaultOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return "Invalid Date";
  }
};

/**
 * Format time from ISO string or Date object
 * @param {string|Date} time - Time to format
 * @param {boolean} includeSeconds - Whether to include seconds
 * @returns {string} - Formatted time string (e.g., 14:30)
 */
export const formatTime = (time, includeSeconds = false) => {
  if (!time) return "N/A";
  
  try {
    const timeObj = typeof time === 'string' ? new Date(time) : time;
    return timeObj.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: includeSeconds ? '2-digit' : undefined
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return "Invalid Time";
  }
};
