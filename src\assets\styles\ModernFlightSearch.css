/* Modern Flight Search Design with Custom Color Palette */
:root {
  --primary-light: #8ecae6; /* Light blue */
  --primary-medium: #219ebc; /* Medium blue */
  --primary-dark: #023047; /* Dark blue */
  --accent-yellow: #ffb703; /* Yellow */
  --accent-orange: #fb8500; /* Orange */

  /* Additional colors for better design */
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;
  --shadow: rgba(2, 48, 71, 0.1);
  --shadow-hover: rgba(2, 48, 71, 0.2);
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
}

/* Main Container */
.container-fluid.bg-info {
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    rgba(142, 202, 230, 0.3) 100%
  ) !important;
  min-height: 100vh;
  padding: 2rem 0;
}

/* Search Form Card */
.card.shadow-sm {
  border: none;
  border-radius: 20px;
  box-shadow: 0 10px 30px var(--shadow) !important;
  background: var(--white);
  overflow: hidden;
}

.modern-card-header {
  background: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary-medium) 100%
  );
  color: var(--white);
  padding: 2rem;
  text-align: center;
  border: none;
}

.modern-card-header h3 {
  color: var(--white);
  font-weight: 700;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.modern-card-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.card-body {
  padding: 2.5rem !important;
}

/* Trip Type Selector */
.trip-type-selector {
  margin-bottom: 2rem;
}

.trip-type-selector h5 {
  color: var(--primary-dark);
  font-weight: 600;
  margin-bottom: 1rem;
}

.btn-group {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(33, 158, 188, 0.2);
}

.btn-outline-primary {
  border-color: var(--primary-medium);
  color: var(--primary-medium);
  background: var(--white);
  border-radius: 0;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
  background: var(--primary-medium);
  border-color: var(--primary-medium);
  color: var(--white);
  transform: translateY(-1px);
}

.btn-check:checked + .btn-outline-primary {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  border-color: var(--accent-yellow);
  color: var(--primary-dark);
  font-weight: 600;
}

/* Form Labels */
.form-label {
  color: var(--primary-dark);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

/* Search Fields Container */
.search-fields-container {
  background: rgba(142, 202, 230, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Swap Button */
.swap-button {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 2px solid var(--primary-medium);
  background: var(--white);
  color: var(--primary-medium);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.8rem;
}

.swap-button:hover {
  background: var(--primary-medium);
  color: var(--white);
  transform: rotate(180deg) scale(1.1);
  box-shadow: 0 4px 15px rgba(33, 158, 188, 0.3);
}

/* Input Fields */
.form-control,
.form-select {
  border: 2px solid rgba(142, 202, 230, 0.3);
  border-radius: 10px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--accent-yellow);
  box-shadow: 0 0 0 3px rgba(255, 183, 3, 0.2);
  background: var(--white);
}

.form-control.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  color: var(--danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Passenger Section */
.passenger-section {
  background: rgba(255, 183, 3, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
}

.passenger-section h5 {
  color: var(--primary-dark);
  font-weight: 600;
  margin-bottom: 1rem;
}

.passenger-type-card {
  background: var(--white);
  border-radius: 12px;
  padding: 1.25rem;
  border: 2px solid rgba(142, 202, 230, 0.2);
  transition: all 0.3s ease;
  min-width: 200px;
}

.passenger-type-card:hover {
  border-color: var(--primary-medium);
  box-shadow: 0 4px 15px rgba(33, 158, 188, 0.2);
}

.passenger-type-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-weight: 600;
}

.passenger-type-header i {
  color: var(--accent-orange);
  font-size: 1.1rem;
}

.passenger-type-controls {
  width: 100%;
}

/* Dropdown Styling */
.dropdown-toggle {
  background: var(--white);
  border: 2px solid var(--primary-light);
  border-radius: 8px;
  color: var(--primary-dark);
  font-weight: 500;
  width: 100%;
  text-align: left;
}

.dropdown-toggle:hover,
.dropdown-toggle:focus {
  background: var(--primary-light);
  border-color: var(--primary-medium);
  color: var(--primary-dark);
}

.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--shadow);
  padding: 1rem;
  min-width: 280px;
}

.dropdown-item {
  border: none;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  background: rgba(142, 202, 230, 0.1);
}

.input-group {
  margin-top: 0.5rem;
}

.input-group .btn {
  border: 1px solid var(--primary-medium);
  background: var(--white);
  color: var(--primary-medium);
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-group .btn:hover {
  background: var(--primary-medium);
  color: var(--white);
}

.input-group .form-control {
  border: 1px solid var(--primary-medium);
  text-align: center;
  font-weight: 600;
  height: 35px;
}

/* Search Button */
.search-button-container {
  background: rgba(255, 183, 3, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.search-button {
  background: linear-gradient(
    45deg,
    var(--accent-yellow),
    var(--accent-orange)
  );
  border: none;
  border-radius: 15px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(255, 183, 3, 0.3);
  position: relative;
  overflow: hidden;
}

.search-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.search-button:hover::before {
  left: 100%;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 183, 3, 0.4);
  color: var(--primary-dark);
}

.search-button:disabled {
  opacity: 0.7;
  transform: none;
  cursor: not-allowed;
}

.search-button-container p {
  color: var(--medium-gray);
  font-size: 0.875rem;
}

/* Modify Search Section */
.modify-search-container {
  margin: 2rem 0;
}

.modify-search-container .card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px var(--shadow);
  background: var(--white);
}

.search-summary {
  text-align: left;
}

.modify-btn {
  background: var(--primary-medium);
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  color: var(--white);
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.modify-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(2, 48, 71, 0.3);
  color: var(--white);
}

.btn-secondary {
  background: var(--primary-medium);
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  color: var(--white);
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(2, 48, 71, 0.3);
  color: var(--white);
}

/* Alert Styling */
.alert-danger {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 10px;
  color: var(--danger);
}

/* Results Section */
.card.shadow-sm.mb-4 {
  border: none;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow) !important;
}

.card-body h4 {
  color: var(--primary-dark);
  font-weight: 600;
}

.alert-primary {
  background: linear-gradient(
    45deg,
    var(--primary-medium),
    var(--primary-light)
  );
  border: none;
  color: var(--white);
  font-weight: 600;
  margin: 0;
}

/* Flexible Date Search Styling */
.bg-secondary {
  background: rgba(142, 202, 230, 0.1) !important;
  border-radius: 15px;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-card-header {
    padding: 1.5rem;
  }

  .modern-card-header h3 {
    font-size: 1.3rem;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  .search-fields-container {
    padding: 1rem;
  }

  .passenger-section {
    padding: 1rem;
  }

  .passenger-type-card {
    margin-bottom: 1rem;
    min-width: 100%;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .btn-outline-primary {
    border-radius: 8px !important;
    margin-bottom: 0.5rem;
  }

  .swap-button {
    margin-top: 1rem;
    margin-bottom: 1rem;
    align-self: center;
  }

  .search-summary {
    text-align: center;
    margin-bottom: 1rem;
  }

  .modify-search-container .d-flex {
    flex-direction: column;
    gap: 1rem !important;
  }
}

@media (max-width: 576px) {
  .container-fluid {
    padding: 1rem 0.5rem;
  }

  .dropdown-menu {
    min-width: 250px;
  }

  .search-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Animation for form appearance */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card.shadow-sm {
  animation: slideInUp 0.6s ease-out;
}

/* Loading state */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: 2px solid var(--accent-yellow);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (forced-colors: active) {
  .btn-outline-primary {
    border: 2px solid ButtonBorder;
  }

  .btn-check:checked + .btn-outline-primary {
    background: Highlight;
    color: HighlightText;
  }

  .search-button {
    background: ButtonFace;
    color: ButtonText;
    border: 2px solid ButtonBorder;
  }
}
