/* FlightBooking.css */

.container {
  padding: 20px;
  max-width: auto;
  margin: auto;
  font-family: Arial, sans-serif;
  margin-bottom: 10px;
  

h2 {
  text-align: center;
  color: #333;
}

form {
  display: flex;
  flex-direction: row;
  gap: 15px;
  margin-top: 20px;
  /* border: 1px solid black; */
  margin-bottom: 50px;
  padding: 20px;
}

form div {
  display: flex;
  flex-direction: column;
  /* border:2px solid green; */
}

label {
  font-weight: bold;
  margin-bottom: 5px;
}

/* input { }*/

.inp {
  /* border: 2px solid green; */
  /* width: 200px; */
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  /* border: 1px solid red; */
}

button {
  background-color: skyblue;
  color: black;
  padding: 2px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  width: 200px;
}

button:hover {
  background-color: #0056b3;
  color: white;
}

.flight-list {
  margin-top: 20px;
  /* border: 2px solid red; */
}

.flight-list h3 {
  color: #007bff;
  margin-bottom: 10px;
}

.flight-list ul {
  list-style: none;
  padding: 0;
}

.flight-list li {
  padding: 10px;
  /* border: 5px solid black; */
  border-radius: 5px;
  margin-bottom: 10px;
}

/* modal css */
.modal {
  display: block; /* Make sure the modal is visible */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content {
  position: relative;
}

/* loader css */
/* HTML: <div class="loader"></div> */
.loader {
  width: fit-content;
  font-weight: bold;
  font-family: monospace;
  font-size: 15px;
  clip-path: inset(0 3ch 0 0);
  animation: l4 1s steps(4) infinite;
}
.loader:before {
  content: "ব্যাটা অপেক্ষা কর...";
}
@keyframes l4 {
  to {
    clip-path: inset(0 -1ch 0 0);
  }
}
/* ========= */
/* Modal Overlay Styles */
.pricing-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.pricing-modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* Pricing Modal Styles */
.pricing-modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pricing-modal-container {
    width: 95%;
    max-height: 85vh;
  }
}
/* মডাল স্টাইল (অন্যান্য স্টাইল রিমুভ করুন) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  /* টেস্টিং জন্য */
  border: 2px dashed red;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  /* টেস্টিং জন্য */
  border: 2px dashed blue;
}

/* এনিমেশন */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  animation: modalFadeIn 0.3s ease-out;
}

/* নিশ্চিত করুন এই স্টাইলগুলো আছে */
.pricing-select-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  width: 100%;
}

.price-option {
  padding: 15px;
  margin: 10px 0;
  border: 1px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
}

.price-option.selected {
  border-color: #007bff;
  background-color: #f0f8ff;
}

/* সাময়িকভাবে যোগ করুন */
.modal-overlay {
  background-color: rgba(255, 0, 0, 0.5) !important; /* লাল রঙের ওভারলে */
}
